<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/FFXIII/USS/ATBGauge.uss?fileID=7433441132597879392&amp;guid=atbgauge&amp;type=3#ATBGauge" />
    
    <!-- Individual ATB Gauge Template -->
    <ui:VisualElement name="atb-gauge-template" class="atb-gauge">
        
        <!-- Character Name -->
        <ui:Label name="character-name" text="Character" class="atb-character-name" />
        
        <!-- ATB Segments Container -->
        <ui:VisualElement name="atb-segments" class="atb-segments">
            
            <!-- Segment 1 -->
            <ui:VisualElement name="atb-segment-0" class="atb-segment" />
            
            <!-- Segment 2 -->
            <ui:VisualElement name="atb-segment-1" class="atb-segment" />
            
            <!-- Segment 3 -->
            <ui:VisualElement name="atb-segment-2" class="atb-segment" />
            
        </ui:VisualElement>
        
        <!-- Optional: Paradigm Role Indicator -->
        <ui:VisualElement name="paradigm-role" class="paradigm-role" style="display: none;">
            <ui:Label name="role-text" text="C" class="role-text" />
        </ui:VisualElement>
        
    </ui:VisualElement>
</ui:UXML>
