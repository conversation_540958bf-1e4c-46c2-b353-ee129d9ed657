/* ATB Gauge Specific Styles for FF XIII UI */

/* ATB Container */
.atb-container {
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 5px;
    border: 1px solid rgba(74, 144, 226, 0.5);
    padding: 10px;
    min-width: 250px;
    max-width: 300px;
}

.atb-title {
    color: rgb(255, 255, 255);
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    -unity-text-align: middle-center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.atb-gauges {
    flex-direction: column;
    gap: 8px;
}

/* Individual ATB Gauge */
.atb-gauge {
    flex-direction: row;
    align-items: center;
    padding: 6px 8px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid rgba(74, 144, 226, 0.3);
    transition: all 0.3s ease-in-out;
}

.atb-gauge:hover {
    background-color: rgba(74, 144, 226, 0.1);
    border-color: rgba(74, 144, 226, 0.6);
}

.atb-gauge.active {
    background-color: rgba(106, 176, 255, 0.2);
    border-color: rgb(106, 176, 255);
    border-width: 2px;
}

.atb-gauge.dead {
    opacity: 0.5;
    background-color: rgba(50, 50, 50, 0.3);
    border-color: rgba(100, 100, 100, 0.3);
}

/* Character Name in ATB Gauge */
.atb-character-name {
    color: rgb(255, 255, 255);
    font-size: 14px;
    font-weight: bold;
    width: 80px;
    min-width: 80px;
    margin-right: 10px;
    -unity-text-align: middle-left;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.atb-gauge.active .atb-character-name {
    color: rgb(106, 176, 255);
}

.atb-gauge.dead .atb-character-name {
    color: rgb(150, 150, 150);
}

/* ATB Segments Container */
.atb-segments {
    flex-direction: row;
    height: 18px;
    width: 120px;
    background-color: rgb(30, 30, 30);
    border-radius: 9px;
    padding: 2px;
    border: 1px solid rgba(74, 144, 226, 0.4);
    overflow: hidden;
}

.atb-gauge.active .atb-segments {
    border-color: rgb(106, 176, 255);
    background-color: rgb(20, 20, 20);
}

.atb-gauge.dead .atb-segments {
    background-color: rgb(40, 40, 40);
    border-color: rgba(100, 100, 100, 0.4);
}

/* Individual ATB Segment */
.atb-segment {
    flex-grow: 1;
    height: 100%;
    margin-right: 2px;
    background-color: rgb(50, 50, 50);
    border-radius: 3px;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.atb-segment:last-child {
    margin-right: 0;
}

/* Empty Segment */
.atb-segment {
    background-color: rgb(50, 50, 50);
    border: 1px solid rgba(100, 100, 100, 0.3);
}

/* Filled Segment */
.atb-segment.filled {
    background-color: rgb(106, 176, 255);
    border: 1px solid rgb(150, 200, 255);
    box-shadow: 0 0 4px rgba(106, 176, 255, 0.6);
}

/* Charging Segment */
.atb-segment.charging {
    background-color: rgb(74, 144, 226);
    border: 1px solid rgb(106, 176, 255);
    animation: atb-charging 1s ease-in-out infinite alternate;
}

/* ATB Charging Animation */
@keyframes atb-charging {
    0% {
        background-color: rgb(74, 144, 226);
        box-shadow: 0 0 2px rgba(74, 144, 226, 0.4);
    }
    100% {
        background-color: rgb(106, 176, 255);
        box-shadow: 0 0 6px rgba(106, 176, 255, 0.8);
    }
}

/* Active Character Glow Effect */
.atb-gauge.active {
    animation: atb-active-glow 2s ease-in-out infinite alternate;
}

@keyframes atb-active-glow {
    0% {
        border-color: rgb(106, 176, 255);
        box-shadow: 0 0 4px rgba(106, 176, 255, 0.4);
    }
    100% {
        border-color: rgb(150, 200, 255);
        box-shadow: 0 0 8px rgba(106, 176, 255, 0.8);
    }
}

/* Segment Fill Animation */
.atb-segment.filling {
    animation: atb-segment-fill 0.5s ease-out forwards;
}

@keyframes atb-segment-fill {
    0% {
        background-color: rgb(50, 50, 50);
        transform: scaleX(0);
    }
    50% {
        background-color: rgb(74, 144, 226);
        transform: scaleX(0.5);
    }
    100% {
        background-color: rgb(106, 176, 255);
        transform: scaleX(1);
    }
}

/* Segment Empty Animation */
.atb-segment.emptying {
    animation: atb-segment-empty 0.3s ease-in forwards;
}

@keyframes atb-segment-empty {
    0% {
        background-color: rgb(106, 176, 255);
        opacity: 1;
    }
    100% {
        background-color: rgb(50, 50, 50);
        opacity: 0.7;
    }
}

/* Paradigm Role Indicator (if applicable) */
.atb-gauge .paradigm-role {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    background-color: rgb(26, 58, 110);
    border: 1px solid rgb(74, 144, 226);
    border-radius: 8px;
    font-size: 8px;
    color: rgb(255, 255, 255);
    -unity-text-align: middle-center;
}

/* ATB Segment Progress Bar (for partial filling) */
.atb-segment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, 
        rgba(74, 144, 226, 0.8) 0%, 
        rgba(106, 176, 255, 1) 100%);
    border-radius: 3px;
    transition: width 0.3s ease-out;
}

.atb-segment.charging::before {
    width: 100%;
    animation: atb-progress-pulse 1s ease-in-out infinite;
}

@keyframes atb-progress-pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1280px) {
    .atb-container {
        min-width: 200px;
    }
    
    .atb-character-name {
        width: 60px;
        min-width: 60px;
        font-size: 12px;
    }
    
    .atb-segments {
        width: 100px;
        height: 16px;
    }
}

@media (max-width: 1024px) {
    .atb-container {
        min-width: 180px;
    }
    
    .atb-character-name {
        width: 50px;
        min-width: 50px;
        font-size: 11px;
    }
    
    .atb-segments {
        width: 90px;
        height: 14px;
    }
    
    .atb-segment {
        border-radius: 2px;
    }
}

/* High Contrast Mode */
.high-contrast .atb-segment.filled {
    background-color: rgb(255, 255, 0);
    border-color: rgb(255, 255, 255);
}

.high-contrast .atb-segment.charging {
    background-color: rgb(255, 165, 0);
    border-color: rgb(255, 255, 255);
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .atb-segment,
    .atb-gauge {
        animation: none;
        transition: none;
    }
    
    .atb-segment.charging {
        background-color: rgb(106, 176, 255);
        opacity: 0.8;
    }
}

/* Color Blind Friendly Alternatives */
.colorblind-friendly .atb-segment.filled {
    background-color: rgb(0, 150, 255); /* Blue instead of blue-green */
    border-color: rgb(100, 200, 255);
}

.colorblind-friendly .atb-segment.charging {
    background-color: rgb(255, 200, 0); /* Yellow-orange for charging */
    border-color: rgb(255, 220, 100);
}

/* Debug Mode Styles */
.debug-mode .atb-gauge {
    border: 2px dashed rgb(255, 0, 255);
}

.debug-mode .atb-segment {
    border: 1px solid rgb(255, 255, 0);
}

.debug-mode .atb-segment::after {
    content: attr(data-segment-index);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8px;
    color: rgb(255, 255, 255);
    text-shadow: 1px 1px 1px rgb(0, 0, 0);
}
