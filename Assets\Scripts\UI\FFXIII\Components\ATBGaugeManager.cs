using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.UI.FFXIII.Core;

namespace TacticalCombatSystem.UI.FFXIII.Components
{
    /// <summary>
    /// Manages ATB (Active Time Battle) gauge visualization for all characters
    /// Provides FF XIII-style segmented gauges with real-time updates
    /// </summary>
    public class ATBGaugeManager
    {
        private UIDataBinding dataBinding;
        private FF13StyleManager styleManager;
        private VisualElement container;
        private Dictionary<BaseCharacter, ATBGaugeComponent> characterGauges;
        private BaseCharacter activeCharacter;
        
        // ATB Configuration
        private const int DEFAULT_ATB_SEGMENTS = 3;
        private const float SEGMENT_FILL_DURATION = 0.5f;
        private const float GAUGE_UPDATE_INTERVAL = 0.1f;
        
        public ATBGaugeManager(UIDataBinding dataBinding, FF13StyleManager styleManager)
        {
            this.dataBinding = dataBinding;
            this.styleManager = styleManager;
            this.characterGauges = new Dictionary<BaseCharacter, ATBGaugeComponent>();
        }
        
        #region Initialization
        
        /// <summary>
        /// Initialize the ATB gauge manager with a container
        /// </summary>
        public void Initialize(VisualElement container)
        {
            this.container = container;
            
            if (container != null)
            {
                // Clear any existing content
                container.Clear();
                
                // Set up container styling
                styleManager.ApplyATBGaugeStyles(container.parent);
            }
        }
        
        /// <summary>
        /// Setup ATB gauges for a list of characters
        /// </summary>
        public void SetupCharacters(List<ICombatParticipant> characters)
        {
            if (container == null) return;
            
            // Clear existing gauges
            ClearGauges();
            
            // Create gauges for each character
            foreach (var participant in characters)
            {
                if (participant is BaseCharacter character)
                {
                    CreateCharacterGauge(character);
                }
            }
        }
        
        /// <summary>
        /// Create an ATB gauge for a specific character
        /// </summary>
        private void CreateCharacterGauge(BaseCharacter character)
        {
            if (character == null || container == null) return;
            
            // Create gauge component
            var gaugeComponent = new ATBGaugeComponent(character, dataBinding, styleManager);
            
            // Create visual element for the gauge
            var gaugeElement = CreateGaugeElement(character);
            container.Add(gaugeElement);
            
            // Initialize the component
            gaugeComponent.Initialize(gaugeElement);
            
            // Store reference
            characterGauges[character] = gaugeComponent;
            
            // Create data bindings for this character
            CreateCharacterBindings(character);
        }
        
        /// <summary>
        /// Create the visual element structure for an ATB gauge
        /// </summary>
        private VisualElement CreateGaugeElement(BaseCharacter character)
        {
            var gaugeElement = new VisualElement();
            gaugeElement.name = $"atb-gauge-{character.Name}";
            gaugeElement.AddToClassList("atb-gauge");
            
            // Character name label
            var nameLabel = new Label(character.Name);
            nameLabel.name = "character-name";
            nameLabel.AddToClassList("atb-character-name");
            gaugeElement.Add(nameLabel);
            
            // ATB segments container
            var segmentsContainer = new VisualElement();
            segmentsContainer.name = "atb-segments";
            segmentsContainer.AddToClassList("atb-segments");
            
            // Create individual segments
            int segmentCount = GetCharacterATBSegments(character);
            for (int i = 0; i < segmentCount; i++)
            {
                var segment = new VisualElement();
                segment.name = $"atb-segment-{i}";
                segment.AddToClassList("atb-segment");
                segmentsContainer.Add(segment);
            }
            
            gaugeElement.Add(segmentsContainer);
            
            return gaugeElement;
        }
        
        /// <summary>
        /// Create data bindings for a character's ATB data
        /// </summary>
        private void CreateCharacterBindings(BaseCharacter character)
        {
            var prefix = $"atb_{character.Name}";
            
            // ATB progress (0.0 to 1.0 per segment)
            var progressProperty = dataBinding.CreateProperty($"{prefix}_progress", 0f);
            
            // Number of filled segments
            var filledSegmentsProperty = dataBinding.CreateProperty($"{prefix}_filledSegments", 0);
            
            // Current charging segment progress
            var chargingProgressProperty = dataBinding.CreateProperty($"{prefix}_chargingProgress", 0f);
            
            // Active state
            var isActiveProperty = dataBinding.CreateProperty($"{prefix}_isActive", false);
            
            // Can act (has at least one filled segment)
            var canActProperty = dataBinding.CreateProperty($"{prefix}_canAct", false);
        }
        
        #endregion
        
        #region ATB Management
        
        /// <summary>
        /// Set the active character (whose turn it is)
        /// </summary>
        public void SetActiveCharacter(BaseCharacter character)
        {
            // Update previous active character
            if (activeCharacter != null && characterGauges.TryGetValue(activeCharacter, out var prevGauge))
            {
                prevGauge.SetActive(false);
                dataBinding.SetProperty($"atb_{activeCharacter.Name}_isActive", false);
            }
            
            // Update new active character
            activeCharacter = character;
            if (character != null && characterGauges.TryGetValue(character, out var newGauge))
            {
                newGauge.SetActive(true);
                dataBinding.SetProperty($"atb_{character.Name}_isActive", true);
                
                // Fill ATB segments for player turn (adapted for turn-based)
                FillATBSegments(character);
            }
        }
        
        /// <summary>
        /// Fill ATB segments for a character (simulates charging for turn-based)
        /// </summary>
        private void FillATBSegments(BaseCharacter character)
        {
            if (character == null || !characterGauges.TryGetValue(character, out var gauge)) return;
            
            int maxSegments = GetCharacterATBSegments(character);
            
            // For turn-based adaptation: fill all segments when it's the character's turn
            for (int i = 0; i < maxSegments; i++)
            {
                gauge.FillSegment(i, true);
            }
            
            // Update data bindings
            dataBinding.SetProperty($"atb_{character.Name}_filledSegments", maxSegments);
            dataBinding.SetProperty($"atb_{character.Name}_canAct", true);
        }
        
        /// <summary>
        /// Consume ATB segments when an action is used
        /// </summary>
        public void ConsumeATBSegments(BaseCharacter character, int segmentCost)
        {
            if (character == null || !characterGauges.TryGetValue(character, out var gauge)) return;
            
            var prefix = $"atb_{character.Name}";
            int currentFilled = dataBinding.GetPropertyValue<int>($"{prefix}_filledSegments");
            int newFilled = Mathf.Max(0, currentFilled - segmentCost);
            
            // Update visual segments
            int maxSegments = GetCharacterATBSegments(character);
            for (int i = newFilled; i < maxSegments; i++)
            {
                gauge.EmptySegment(i);
            }
            
            // Update data bindings
            dataBinding.SetProperty($"{prefix}_filledSegments", newFilled);
            dataBinding.SetProperty($"{prefix}_canAct", newFilled > 0);
        }
        
        /// <summary>
        /// Handle turn end for a character
        /// </summary>
        public void OnTurnEnded(BaseCharacter character)
        {
            if (character == null || !characterGauges.TryGetValue(character, out var gauge)) return;
            
            // Empty all segments at turn end
            int maxSegments = GetCharacterATBSegments(character);
            for (int i = 0; i < maxSegments; i++)
            {
                gauge.EmptySegment(i);
            }
            
            // Update data bindings
            var prefix = $"atb_{character.Name}";
            dataBinding.SetProperty($"{prefix}_filledSegments", 0);
            dataBinding.SetProperty($"{prefix}_canAct", false);
            dataBinding.SetProperty($"{prefix}_isActive", false);
            
            gauge.SetActive(false);
        }
        
        /// <summary>
        /// Update character status (health, alive state, etc.)
        /// </summary>
        public void UpdateCharacterStatus(BaseCharacter character)
        {
            if (character == null || !characterGauges.TryGetValue(character, out var gauge)) return;
            
            gauge.UpdateCharacterStatus();
            
            // If character is dead, empty all segments
            if (!character.IsAlive)
            {
                int maxSegments = GetCharacterATBSegments(character);
                for (int i = 0; i < maxSegments; i++)
                {
                    gauge.EmptySegment(i);
                }
                
                var prefix = $"atb_{character.Name}";
                dataBinding.SetProperty($"{prefix}_filledSegments", 0);
                dataBinding.SetProperty($"{prefix}_canAct", false);
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Get the number of ATB segments for a character
        /// </summary>
        private int GetCharacterATBSegments(BaseCharacter character)
        {
            if (character == null) return DEFAULT_ATB_SEGMENTS;
            
            // Could be based on character level, equipment, or paradigm
            // For now, use default
            return DEFAULT_ATB_SEGMENTS;
        }
        
        /// <summary>
        /// Get ATB segment cost for an action
        /// </summary>
        public int GetActionSegmentCost(ICombatAction action)
        {
            if (action == null) return 1;
            
            // Different actions could cost different amounts
            // For now, most actions cost 1 segment
            return 1;
        }
        
        /// <summary>
        /// Check if character can perform an action
        /// </summary>
        public bool CanCharacterAct(BaseCharacter character, ICombatAction action)
        {
            if (character == null || action == null) return false;
            
            var prefix = $"atb_{character.Name}";
            int filledSegments = dataBinding.GetPropertyValue<int>($"{prefix}_filledSegments");
            int requiredSegments = GetActionSegmentCost(action);
            
            return filledSegments >= requiredSegments && character.IsAlive;
        }
        
        /// <summary>
        /// Clear all gauges
        /// </summary>
        private void ClearGauges()
        {
            foreach (var gauge in characterGauges.Values)
            {
                gauge.Cleanup();
            }
            
            characterGauges.Clear();
            
            if (container != null)
            {
                container.Clear();
            }
        }
        
        #endregion
        
        #region Cleanup
        
        /// <summary>
        /// Cleanup the ATB gauge manager
        /// </summary>
        public void Cleanup()
        {
            ClearGauges();
            activeCharacter = null;
            container = null;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Individual ATB gauge component for a single character
    /// </summary>
    public class ATBGaugeComponent
    {
        private BaseCharacter character;
        private UIDataBinding dataBinding;
        private FF13StyleManager styleManager;
        private VisualElement gaugeElement;
        private List<VisualElement> segments;
        private Label nameLabel;
        
        public ATBGaugeComponent(BaseCharacter character, UIDataBinding dataBinding, FF13StyleManager styleManager)
        {
            this.character = character;
            this.dataBinding = dataBinding;
            this.styleManager = styleManager;
            this.segments = new List<VisualElement>();
        }
        
        /// <summary>
        /// Initialize the gauge component
        /// </summary>
        public void Initialize(VisualElement element)
        {
            gaugeElement = element;
            nameLabel = element.Q<Label>("character-name");
            
            // Get all segment elements
            var segmentsContainer = element.Q<VisualElement>("atb-segments");
            if (segmentsContainer != null)
            {
                segments.Clear();
                var segmentElements = segmentsContainer.Query<VisualElement>(className: "atb-segment").ToList();
                segments.AddRange(segmentElements);
            }
            
            // Apply initial styling
            styleManager.StyleATBGauge(gaugeElement);
            
            // Update initial state
            UpdateCharacterStatus();
        }
        
        /// <summary>
        /// Fill a specific segment
        /// </summary>
        public void FillSegment(int segmentIndex, bool animate = false)
        {
            if (segmentIndex < 0 || segmentIndex >= segments.Count) return;
            
            var segment = segments[segmentIndex];
            segment.RemoveFromClassList("charging");
            segment.AddToClassList("filled");
            
            // Could add animation here if needed
        }
        
        /// <summary>
        /// Empty a specific segment
        /// </summary>
        public void EmptySegment(int segmentIndex)
        {
            if (segmentIndex < 0 || segmentIndex >= segments.Count) return;
            
            var segment = segments[segmentIndex];
            segment.RemoveFromClassList("filled");
            segment.RemoveFromClassList("charging");
        }
        
        /// <summary>
        /// Set segment as charging
        /// </summary>
        public void SetSegmentCharging(int segmentIndex)
        {
            if (segmentIndex < 0 || segmentIndex >= segments.Count) return;
            
            var segment = segments[segmentIndex];
            segment.RemoveFromClassList("filled");
            segment.AddToClassList("charging");
        }
        
        /// <summary>
        /// Set the gauge as active (current turn)
        /// </summary>
        public void SetActive(bool active)
        {
            if (gaugeElement == null) return;
            
            if (active)
            {
                gaugeElement.AddToClassList("active");
            }
            else
            {
                gaugeElement.RemoveFromClassList("active");
            }
        }
        
        /// <summary>
        /// Update character status display
        /// </summary>
        public void UpdateCharacterStatus()
        {
            if (character == null || gaugeElement == null) return;
            
            // Update name label
            if (nameLabel != null)
            {
                nameLabel.text = character.Name;
            }
            
            // Update visual state based on character status
            if (!character.IsAlive)
            {
                gaugeElement.AddToClassList("dead");
                
                // Empty all segments
                foreach (var segment in segments)
                {
                    segment.RemoveFromClassList("filled");
                    segment.RemoveFromClassList("charging");
                }
            }
            else
            {
                gaugeElement.RemoveFromClassList("dead");
            }
        }
        
        /// <summary>
        /// Cleanup the component
        /// </summary>
        public void Cleanup()
        {
            segments.Clear();
            gaugeElement = null;
            nameLabel = null;
        }
    }
}
