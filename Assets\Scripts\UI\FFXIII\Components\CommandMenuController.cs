using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.UI.FFXIII.Core;

namespace TacticalCombatSystem.UI.FFXIII.Components
{
    /// <summary>
    /// Controls the FF XIII-style command menu system
    /// Handles action selection, paradigm shifts, and menu navigation
    /// </summary>
    public class CommandMenuController
    {
        private UIDataBinding dataBinding;
        private UIAnimationController animationController;
        private VisualElement container;
        private BaseCharacter currentCharacter;
        
        // Menu Elements
        private VisualElement mainMenu;
        private VisualElement attackSubmenu;
        private VisualElement magicSubmenu;
        private VisualElement itemsSubmenu;
        private VisualElement paradigmSubmenu;
        
        // Current Menu State
        private MenuState currentState;
        private int selectedIndex;
        private List<Button> currentMenuButtons;
        
        // Events
        public event Action<ICombatAction> OnActionSelected;
        public event Action OnMenuClosed;
        
        // Menu Configuration
        private const float MENU_TRANSITION_DURATION = 0.3f;
        private const float BUTTON_HOVER_SCALE = 1.05f;
        
        public CommandMenuController(UIDataBinding dataBinding, UIAnimationController animationController)
        {
            this.dataBinding = dataBinding;
            this.animationController = animationController;
            this.currentMenuButtons = new List<Button>();
            this.currentState = MenuState.Hidden;
            this.selectedIndex = 0;
        }
        
        #region Initialization
        
        /// <summary>
        /// Initialize the command menu controller
        /// </summary>
        public void Initialize(VisualElement container)
        {
            this.container = container;
            
            if (container != null)
            {
                SetupMenuElements();
                SetupEventHandlers();
                HideAllMenus();
            }
        }
        
        /// <summary>
        /// Setup references to menu elements
        /// </summary>
        private void SetupMenuElements()
        {
            mainMenu = container.Q<VisualElement>("main-menu");
            attackSubmenu = container.Q<VisualElement>("attack-submenu");
            magicSubmenu = container.Q<VisualElement>("magic-submenu");
            itemsSubmenu = container.Q<VisualElement>("items-submenu");
            paradigmSubmenu = container.Q<VisualElement>("paradigm-submenu");
        }
        
        /// <summary>
        /// Setup event handlers for menu buttons
        /// </summary>
        private void SetupEventHandlers()
        {
            // Main menu buttons
            SetupButton("attack-button", () => ShowSubmenu(MenuState.AttackSubmenu));
            SetupButton("magic-button", () => ShowSubmenu(MenuState.MagicSubmenu));
            SetupButton("items-button", () => ShowSubmenu(MenuState.ItemsSubmenu));
            SetupButton("paradigm-button", () => ShowSubmenu(MenuState.ParadigmSubmenu));
            SetupButton("defend-button", () => SelectDefendAction());
            
            // Back buttons in submenus
            SetupBackButtons();
            
            // Keyboard navigation
            container.RegisterCallback<KeyDownEvent>(OnKeyDown);
            container.focusable = true;
        }
        
        /// <summary>
        /// Setup a button with click handler and styling
        /// </summary>
        private void SetupButton(string buttonName, Action clickHandler)
        {
            var button = container.Q<Button>(buttonName);
            if (button != null)
            {
                button.clicked += clickHandler;
                
                // Add hover effects
                button.RegisterCallback<MouseEnterEvent>(evt => OnButtonHover(button, true));
                button.RegisterCallback<MouseLeaveEvent>(evt => OnButtonHover(button, false));
            }
        }
        
        /// <summary>
        /// Setup back buttons for all submenus
        /// </summary>
        private void SetupBackButtons()
        {
            var backButtons = container.Query<Button>("back-button").ToList();
            foreach (var backButton in backButtons)
            {
                backButton.clicked += () => ShowMainMenu();
            }
        }
        
        #endregion
        
        #region Menu Display Control
        
        /// <summary>
        /// Show the command menu for a character
        /// </summary>
        public void ShowMenu(BaseCharacter character)
        {
            currentCharacter = character;
            
            if (container != null)
            {
                container.style.display = DisplayStyle.Flex;
                ShowMainMenu();
                
                // Animate menu appearance
                animationController.SlideIn(container, new Vector2(0, 100), MENU_TRANSITION_DURATION);
                animationController.FadeIn(container, MENU_TRANSITION_DURATION);
                
                // Focus for keyboard navigation
                container.Focus();
            }
        }
        
        /// <summary>
        /// Hide the command menu
        /// </summary>
        public void HideMenu()
        {
            if (container != null)
            {
                animationController.SlideOut(container, new Vector2(0, 100), MENU_TRANSITION_DURATION);
                animationController.FadeOut(container, MENU_TRANSITION_DURATION, () =>
                {
                    container.style.display = DisplayStyle.None;
                    HideAllMenus();
                });
            }
            
            currentState = MenuState.Hidden;
            currentCharacter = null;
            OnMenuClosed?.Invoke();
        }
        
        /// <summary>
        /// Show the main menu
        /// </summary>
        private void ShowMainMenu()
        {
            HideAllMenus();
            
            if (mainMenu != null)
            {
                mainMenu.style.display = DisplayStyle.Flex;
                currentState = MenuState.MainMenu;
                selectedIndex = 0;
                
                // Update available actions based on character
                UpdateMainMenuButtons();
                
                // Animate transition
                animationController.FadeIn(mainMenu, MENU_TRANSITION_DURATION);
            }
        }
        
        /// <summary>
        /// Show a specific submenu
        /// </summary>
        private void ShowSubmenu(MenuState submenuState)
        {
            HideAllMenus();
            
            VisualElement submenu = GetSubmenuElement(submenuState);
            if (submenu != null)
            {
                submenu.style.display = DisplayStyle.Flex;
                currentState = submenuState;
                selectedIndex = 0;
                
                // Populate submenu with actions
                PopulateSubmenu(submenuState);
                
                // Animate transition
                animationController.SlideIn(submenu, new Vector2(-50, 0), MENU_TRANSITION_DURATION);
                animationController.FadeIn(submenu, MENU_TRANSITION_DURATION);
            }
        }
        
        /// <summary>
        /// Hide all menu elements
        /// </summary>
        private void HideAllMenus()
        {
            var menus = new[] { mainMenu, attackSubmenu, magicSubmenu, itemsSubmenu, paradigmSubmenu };
            foreach (var menu in menus)
            {
                if (menu != null)
                {
                    menu.style.display = DisplayStyle.None;
                }
            }
        }
        
        #endregion
        
        #region Menu Population
        
        /// <summary>
        /// Update main menu buttons based on character abilities
        /// </summary>
        private void UpdateMainMenuButtons()
        {
            if (currentCharacter == null) return;
            
            // Enable/disable buttons based on character abilities and resources
            var attackButton = container.Q<Button>("attack-button");
            var magicButton = container.Q<Button>("magic-button");
            var itemsButton = container.Q<Button>("items-button");
            var paradigmButton = container.Q<Button>("paradigm-button");
            var defendButton = container.Q<Button>("defend-button");
            
            // Attack is always available
            SetButtonEnabled(attackButton, true);
            
            // Magic requires MP
            SetButtonEnabled(magicButton, currentCharacter.CurrentMana > 0);
            
            // Items - check if character has usable items
            SetButtonEnabled(itemsButton, HasUsableItems());
            
            // Paradigm shift - could be limited by cooldown or other factors
            SetButtonEnabled(paradigmButton, CanUseParadigmShift());
            
            // Defend is always available
            SetButtonEnabled(defendButton, true);
            
            // Update current menu buttons list for navigation
            UpdateCurrentMenuButtons();
        }
        
        /// <summary>
        /// Populate a submenu with available actions
        /// </summary>
        private void PopulateSubmenu(MenuState submenuState)
        {
            if (currentCharacter == null) return;
            
            switch (submenuState)
            {
                case MenuState.AttackSubmenu:
                    PopulateAttackSubmenu();
                    break;
                case MenuState.MagicSubmenu:
                    PopulateMagicSubmenu();
                    break;
                case MenuState.ItemsSubmenu:
                    PopulateItemsSubmenu();
                    break;
                case MenuState.ParadigmSubmenu:
                    PopulateParadigmSubmenu();
                    break;
            }
        }
        
        /// <summary>
        /// Populate attack submenu with available attacks
        /// </summary>
        private void PopulateAttackSubmenu()
        {
            var attackList = attackSubmenu?.Q<ScrollView>("attack-list");
            if (attackList == null) return;
            
            attackList.Clear();
            
            // Get character's attack abilities
            var attacks = GetCharacterAttacks();
            foreach (var attack in attacks)
            {
                CreateActionButton(attackList, attack);
            }
        }
        
        /// <summary>
        /// Populate magic submenu with available spells
        /// </summary>
        private void PopulateMagicSubmenu()
        {
            var magicList = magicSubmenu?.Q<ScrollView>("magic-list");
            if (magicList == null) return;
            
            magicList.Clear();
            
            // Get character's magic abilities
            var spells = GetCharacterSpells();
            foreach (var spell in spells)
            {
                CreateActionButton(magicList, spell);
            }
        }
        
        /// <summary>
        /// Populate items submenu with usable items
        /// </summary>
        private void PopulateItemsSubmenu()
        {
            var itemsList = itemsSubmenu?.Q<ScrollView>("items-list");
            if (itemsList == null) return;
            
            itemsList.Clear();
            
            // Get usable items
            var items = GetUsableItems();
            foreach (var item in items)
            {
                CreateActionButton(itemsList, item);
            }
        }
        
        /// <summary>
        /// Populate paradigm submenu with available paradigms
        /// </summary>
        private void PopulateParadigmSubmenu()
        {
            var paradigmList = paradigmSubmenu?.Q<VisualElement>("paradigm-list");
            if (paradigmList == null) return;
            
            paradigmList.Clear();
            
            // Get available paradigms
            var paradigms = GetAvailableParadigms();
            foreach (var paradigm in paradigms)
            {
                CreateParadigmOption(paradigmList, paradigm);
            }
        }
        
        /// <summary>
        /// Create an action button for a submenu
        /// </summary>
        private void CreateActionButton(VisualElement parent, ICombatAction action)
        {
            var button = new Button();
            button.text = action.ActionName;
            button.AddToClassList("action-button");
            
            // Add cost information if applicable
            if (action.mpCost > 0)
            {
                button.text += $" (MP: {action.mpCost})";
            }
            
            // Check if action is usable
            bool canUse = CanUseAction(action);
            SetButtonEnabled(button, canUse);
            
            // Add click handler
            button.clicked += () => SelectAction(action);
            
            // Add hover effects
            button.RegisterCallback<MouseEnterEvent>(evt => OnButtonHover(button, true));
            button.RegisterCallback<MouseLeaveEvent>(evt => OnButtonHover(button, false));
            
            parent.Add(button);
        }
        
        /// <summary>
        /// Create a paradigm option element
        /// </summary>
        private void CreateParadigmOption(VisualElement parent, ParadigmData paradigm)
        {
            var option = new VisualElement();
            option.AddToClassList("paradigm-option");
            
            // Add paradigm roles
            var rolesContainer = new VisualElement();
            rolesContainer.AddToClassList("paradigm-roles");
            
            foreach (var role in paradigm.Roles)
            {
                var roleLabel = new Label(role.ToString().Substring(0, 1)); // First letter
                roleLabel.AddToClassList("paradigm-role");
                rolesContainer.Add(roleLabel);
            }
            
            option.Add(rolesContainer);
            
            // Add click handler
            option.RegisterCallback<ClickEvent>(evt => SelectParadigm(paradigm));
            
            // Add hover effects
            option.RegisterCallback<MouseEnterEvent>(evt => OnParadigmHover(option, true));
            option.RegisterCallback<MouseLeaveEvent>(evt => OnParadigmHover(option, false));
            
            parent.Add(option);
        }
        
        #endregion
        
        #region Action Selection
        
        /// <summary>
        /// Select an action and notify listeners
        /// </summary>
        private void SelectAction(ICombatAction action)
        {
            if (action != null && CanUseAction(action))
            {
                OnActionSelected?.Invoke(action);
                HideMenu();
            }
        }
        
        /// <summary>
        /// Select defend action
        /// </summary>
        private void SelectDefendAction()
        {
            // Create a defend action (this would typically be a predefined action)
            var defendAction = CreateDefendAction();
            SelectAction(defendAction);
        }
        
        /// <summary>
        /// Select a paradigm shift
        /// </summary>
        private void SelectParadigm(ParadigmData paradigm)
        {
            if (paradigm != null)
            {
                // Create paradigm shift action
                var paradigmAction = CreateParadigmShiftAction(paradigm);
                SelectAction(paradigmAction);
            }
        }
        
        #endregion
        
        #region Input Handling
        
        /// <summary>
        /// Handle keyboard input for menu navigation
        /// </summary>
        private void OnKeyDown(KeyDownEvent evt)
        {
            switch (evt.keyCode)
            {
                case KeyCode.UpArrow:
                    NavigateMenu(-1);
                    evt.StopPropagation();
                    break;
                case KeyCode.DownArrow:
                    NavigateMenu(1);
                    evt.StopPropagation();
                    break;
                case KeyCode.Return:
                case KeyCode.KeypadEnter:
                    ConfirmSelection();
                    evt.StopPropagation();
                    break;
                case KeyCode.Escape:
                    if (currentState == MenuState.MainMenu)
                    {
                        HideMenu();
                    }
                    else
                    {
                        ShowMainMenu();
                    }
                    evt.StopPropagation();
                    break;
            }
        }
        
        /// <summary>
        /// Navigate through menu options
        /// </summary>
        private void NavigateMenu(int direction)
        {
            if (currentMenuButtons.Count == 0) return;
            
            // Update selection index
            selectedIndex = (selectedIndex + direction + currentMenuButtons.Count) % currentMenuButtons.Count;
            
            // Update visual selection
            UpdateMenuSelection();
        }
        
        /// <summary>
        /// Confirm current selection
        /// </summary>
        private void ConfirmSelection()
        {
            if (selectedIndex >= 0 && selectedIndex < currentMenuButtons.Count)
            {
                var selectedButton = currentMenuButtons[selectedIndex];
                if (selectedButton.enabledSelf)
                {
                    // Simulate button click
                    using (var clickEvent = ClickEvent.GetPooled())
                    {
                        clickEvent.target = selectedButton;
                        selectedButton.SendEvent(clickEvent);
                    }
                }
            }
        }
        
        #endregion
        
        #region Helper Methods
        
        /// <summary>
        /// Get the submenu element for a given state
        /// </summary>
        private VisualElement GetSubmenuElement(MenuState state)
        {
            return state switch
            {
                MenuState.AttackSubmenu => attackSubmenu,
                MenuState.MagicSubmenu => magicSubmenu,
                MenuState.ItemsSubmenu => itemsSubmenu,
                MenuState.ParadigmSubmenu => paradigmSubmenu,
                _ => null
            };
        }
        
        /// <summary>
        /// Set button enabled state with visual feedback
        /// </summary>
        private void SetButtonEnabled(Button button, bool enabled)
        {
            if (button == null) return;
            
            button.SetEnabled(enabled);
            
            if (enabled)
            {
                button.RemoveFromClassList("disabled");
            }
            else
            {
                button.AddToClassList("disabled");
            }
        }
        
        /// <summary>
        /// Handle button hover effects
        /// </summary>
        private void OnButtonHover(Button button, bool isHovering)
        {
            if (button == null || !button.enabledSelf) return;
            
            if (isHovering)
            {
                animationController.ScaleBounce(button, BUTTON_HOVER_SCALE, 0.2f);
            }
        }
        
        /// <summary>
        /// Handle paradigm option hover effects
        /// </summary>
        private void OnParadigmHover(VisualElement option, bool isHovering)
        {
            if (option == null) return;
            
            if (isHovering)
            {
                option.AddToClassList("hover");
            }
            else
            {
                option.RemoveFromClassList("hover");
            }
        }
        
        /// <summary>
        /// Update current menu buttons list for navigation
        /// </summary>
        private void UpdateCurrentMenuButtons()
        {
            currentMenuButtons.Clear();
            
            VisualElement currentMenu = currentState switch
            {
                MenuState.MainMenu => mainMenu,
                MenuState.AttackSubmenu => attackSubmenu,
                MenuState.MagicSubmenu => magicSubmenu,
                MenuState.ItemsSubmenu => itemsSubmenu,
                MenuState.ParadigmSubmenu => paradigmSubmenu,
                _ => null
            };
            
            if (currentMenu != null)
            {
                var buttons = currentMenu.Query<Button>().Where(b => b.enabledSelf).ToList();
                currentMenuButtons.AddRange(buttons);
            }
        }
        
        /// <summary>
        /// Update visual selection highlighting
        /// </summary>
        private void UpdateMenuSelection()
        {
            for (int i = 0; i < currentMenuButtons.Count; i++)
            {
                var button = currentMenuButtons[i];
                if (i == selectedIndex)
                {
                    button.AddToClassList("selected");
                    button.Focus();
                }
                else
                {
                    button.RemoveFromClassList("selected");
                }
            }
        }
        
        #endregion
        
        #region Data Access Methods (Placeholder implementations)
        
        private List<ICombatAction> GetCharacterAttacks()
        {
            // Placeholder - would get from character's abilities
            return new List<ICombatAction>();
        }
        
        private List<ICombatAction> GetCharacterSpells()
        {
            // Placeholder - would get from character's magic abilities
            return new List<ICombatAction>();
        }
        
        private List<ICombatAction> GetUsableItems()
        {
            // Placeholder - would get from inventory
            return new List<ICombatAction>();
        }
        
        private List<ParadigmData> GetAvailableParadigms()
        {
            // Placeholder - would get available paradigm configurations
            return new List<ParadigmData>();
        }
        
        private bool HasUsableItems()
        {
            return GetUsableItems().Count > 0;
        }
        
        private bool CanUseParadigmShift()
        {
            return true; // Placeholder
        }
        
        private bool CanUseAction(ICombatAction action)
        {
            if (currentCharacter == null || action == null) return false;
            
            // Check MP cost
            if (action.mpCost > currentCharacter.CurrentMana) return false;
            
            // Check HP cost
            if (action.hpCost > 0 && action.hpCost >= currentCharacter.CurrentHealth) return false;
            
            return true;
        }
        
        private ICombatAction CreateDefendAction()
        {
            // Placeholder - would create a defend action
            return null;
        }
        
        private ICombatAction CreateParadigmShiftAction(ParadigmData paradigm)
        {
            // Placeholder - would create a paradigm shift action
            return null;
        }
        
        #endregion
        
        #region Cleanup
        
        /// <summary>
        /// Cleanup the command menu controller
        /// </summary>
        public void Cleanup()
        {
            currentMenuButtons.Clear();
            currentCharacter = null;
            container = null;
        }
        
        #endregion
    }
    
    /// <summary>
    /// Menu state enumeration
    /// </summary>
    public enum MenuState
    {
        Hidden,
        MainMenu,
        AttackSubmenu,
        MagicSubmenu,
        ItemsSubmenu,
        ParadigmSubmenu
    }
    
    /// <summary>
    /// Paradigm data structure
    /// </summary>
    public class ParadigmData
    {
        public string Name { get; set; }
        public List<ParadigmRole> Roles { get; set; }
        
        public ParadigmData()
        {
            Roles = new List<ParadigmRole>();
        }
    }
    
    /// <summary>
    /// Paradigm role enumeration
    /// </summary>
    public enum ParadigmRole
    {
        Commando,   // Physical damage dealer
        Ravager,    // Magic damage dealer
        Medic,      // Healer
        Sentinel,   // Tank/defender
        Synergist,  // Buffer
        Saboteur    // Debuffer
    }
}
