using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace TacticalCombatSystem.UI.FFXIII.Core
{
    /// <summary>
    /// Handles UI animations and transitions for FF XIII-style interface
    /// Provides smooth, polished animations that enhance the user experience
    /// </summary>
    public class UIAnimationController
    {
        private MonoBehaviour coroutineRunner;
        private Dictionary<VisualElement, Coroutine> activeAnimations;
        
        public UIAnimationController()
        {
            activeAnimations = new Dictionary<VisualElement, Coroutine>();
        }
        
        /// <summary>
        /// Set the MonoBehaviour to use for running coroutines
        /// </summary>
        public void SetCoroutineRunner(MonoBehaviour runner)
        {
            coroutineRunner = runner;
        }
        
        #region Fade Animations
        
        /// <summary>
        /// Fade in an element
        /// </summary>
        public void FadeIn(VisualElement element, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(FadeCoroutine(element, 0f, 1f, duration, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                // Fallback: instant fade
                element.style.opacity = 1f;
                onComplete?.Invoke();
            }
        }
        
        /// <summary>
        /// Fade out an element
        /// </summary>
        public void FadeOut(VisualElement element, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(FadeCoroutine(element, 1f, 0f, duration, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                // Fallback: instant fade
                element.style.opacity = 0f;
                onComplete?.Invoke();
            }
        }
        
        /// <summary>
        /// Fade between two opacity values
        /// </summary>
        public void FadeTo(VisualElement element, float targetOpacity, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            float currentOpacity = element.style.opacity.value;
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(FadeCoroutine(element, currentOpacity, targetOpacity, duration, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                // Fallback: instant change
                element.style.opacity = targetOpacity;
                onComplete?.Invoke();
            }
        }
        
        private IEnumerator FadeCoroutine(VisualElement element, float fromOpacity, float toOpacity, float duration, Action onComplete)
        {
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                float opacity = Mathf.Lerp(fromOpacity, toOpacity, EaseInOutQuad(t));
                
                element.style.opacity = opacity;
                
                yield return null;
            }
            
            element.style.opacity = toOpacity;
            activeAnimations.Remove(element);
            onComplete?.Invoke();
        }
        
        #endregion
        
        #region Scale Animations
        
        /// <summary>
        /// Scale an element with bounce effect
        /// </summary>
        public void ScaleBounce(VisualElement element, float targetScale = 1.2f, float duration = 0.5f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(ScaleBounceCoroutine(element, targetScale, duration, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                onComplete?.Invoke();
            }
        }
        
        /// <summary>
        /// Pulse scale animation
        /// </summary>
        public void ScalePulse(VisualElement element, float pulseScale = 1.1f, float duration = 1f, int pulseCount = -1)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(ScalePulseCoroutine(element, pulseScale, duration, pulseCount));
                activeAnimations[element] = coroutine;
            }
        }
        
        private IEnumerator ScaleBounceCoroutine(VisualElement element, float targetScale, float duration, Action onComplete)
        {
            Vector3 originalScale = Vector3.one;
            float elapsed = 0f;
            
            // Scale up
            while (elapsed < duration * 0.3f)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / (duration * 0.3f);
                float scale = Mathf.Lerp(1f, targetScale, EaseOutBack(t));
                
                element.transform.scale = originalScale * scale;
                
                yield return null;
            }
            
            // Scale down with overshoot
            elapsed = 0f;
            while (elapsed < duration * 0.7f)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / (duration * 0.7f);
                float scale = Mathf.Lerp(targetScale, 1f, EaseOutBounce(t));
                
                element.transform.scale = originalScale * scale;
                
                yield return null;
            }
            
            element.transform.scale = originalScale;
            activeAnimations.Remove(element);
            onComplete?.Invoke();
        }
        
        private IEnumerator ScalePulseCoroutine(VisualElement element, float pulseScale, float duration, int pulseCount)
        {
            Vector3 originalScale = Vector3.one;
            int currentPulse = 0;
            
            while (pulseCount < 0 || currentPulse < pulseCount)
            {
                // Scale up
                float elapsed = 0f;
                while (elapsed < duration * 0.5f)
                {
                    elapsed += Time.deltaTime;
                    float t = elapsed / (duration * 0.5f);
                    float scale = Mathf.Lerp(1f, pulseScale, EaseInOutQuad(t));
                    
                    element.transform.scale = originalScale * scale;
                    
                    yield return null;
                }
                
                // Scale down
                elapsed = 0f;
                while (elapsed < duration * 0.5f)
                {
                    elapsed += Time.deltaTime;
                    float t = elapsed / (duration * 0.5f);
                    float scale = Mathf.Lerp(pulseScale, 1f, EaseInOutQuad(t));
                    
                    element.transform.scale = originalScale * scale;
                    
                    yield return null;
                }
                
                currentPulse++;
            }
            
            element.transform.scale = originalScale;
            activeAnimations.Remove(element);
        }
        
        #endregion
        
        #region Slide Animations
        
        /// <summary>
        /// Slide element in from direction
        /// </summary>
        public void SlideIn(VisualElement element, Vector2 fromOffset, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(SlideCoroutine(element, fromOffset, Vector2.zero, duration, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                element.style.translate = new StyleTranslate(new Translate(0, 0));
                onComplete?.Invoke();
            }
        }
        
        /// <summary>
        /// Slide element out to direction
        /// </summary>
        public void SlideOut(VisualElement element, Vector2 toOffset, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(SlideCoroutine(element, Vector2.zero, toOffset, duration, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                element.style.translate = new StyleTranslate(new Translate(toOffset.x, toOffset.y));
                onComplete?.Invoke();
            }
        }
        
        private IEnumerator SlideCoroutine(VisualElement element, Vector2 fromOffset, Vector2 toOffset, float duration, Action onComplete)
        {
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                Vector2 currentOffset = Vector2.Lerp(fromOffset, toOffset, EaseOutQuart(t));
                
                element.style.translate = new StyleTranslate(new Translate(currentOffset.x, currentOffset.y));
                
                yield return null;
            }
            
            element.style.translate = new StyleTranslate(new Translate(toOffset.x, toOffset.y));
            activeAnimations.Remove(element);
            onComplete?.Invoke();
        }
        
        #endregion
        
        #region Color Animations
        
        /// <summary>
        /// Animate background color
        /// </summary>
        public void AnimateBackgroundColor(VisualElement element, Color fromColor, Color toColor, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(ColorCoroutine(element, fromColor, toColor, duration, true, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                element.style.backgroundColor = toColor;
                onComplete?.Invoke();
            }
        }
        
        /// <summary>
        /// Animate text color
        /// </summary>
        public void AnimateTextColor(VisualElement element, Color fromColor, Color toColor, float duration = 0.3f, Action onComplete = null)
        {
            if (element == null) return;
            
            StopAnimation(element);
            
            if (coroutineRunner != null)
            {
                var coroutine = coroutineRunner.StartCoroutine(ColorCoroutine(element, fromColor, toColor, duration, false, onComplete));
                activeAnimations[element] = coroutine;
            }
            else
            {
                element.style.color = toColor;
                onComplete?.Invoke();
            }
        }
        
        private IEnumerator ColorCoroutine(VisualElement element, Color fromColor, Color toColor, float duration, bool isBackground, Action onComplete)
        {
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                Color currentColor = Color.Lerp(fromColor, toColor, EaseInOutQuad(t));
                
                if (isBackground)
                {
                    element.style.backgroundColor = currentColor;
                }
                else
                {
                    element.style.color = currentColor;
                }
                
                yield return null;
            }
            
            if (isBackground)
            {
                element.style.backgroundColor = toColor;
            }
            else
            {
                element.style.color = toColor;
            }
            
            activeAnimations.Remove(element);
            onComplete?.Invoke();
        }
        
        #endregion
        
        #region Animation Control
        
        /// <summary>
        /// Stop any active animation on an element
        /// </summary>
        public void StopAnimation(VisualElement element)
        {
            if (element != null && activeAnimations.TryGetValue(element, out var coroutine))
            {
                if (coroutineRunner != null && coroutine != null)
                {
                    coroutineRunner.StopCoroutine(coroutine);
                }
                activeAnimations.Remove(element);
            }
        }
        
        /// <summary>
        /// Stop all active animations
        /// </summary>
        public void StopAllAnimations()
        {
            if (coroutineRunner != null)
            {
                foreach (var coroutine in activeAnimations.Values)
                {
                    if (coroutine != null)
                    {
                        coroutineRunner.StopCoroutine(coroutine);
                    }
                }
            }
            
            activeAnimations.Clear();
        }
        
        #endregion
        
        #region Easing Functions
        
        private float EaseInOutQuad(float t)
        {
            return t < 0.5f ? 2f * t * t : -1f + (4f - 2f * t) * t;
        }
        
        private float EaseOutQuart(float t)
        {
            return 1f - Mathf.Pow(1f - t, 4f);
        }
        
        private float EaseOutBack(float t)
        {
            const float c1 = 1.70158f;
            const float c3 = c1 + 1f;
            
            return 1f + c3 * Mathf.Pow(t - 1f, 3f) + c1 * Mathf.Pow(t - 1f, 2f);
        }
        
        private float EaseOutBounce(float t)
        {
            const float n1 = 7.5625f;
            const float d1 = 2.75f;
            
            if (t < 1f / d1)
            {
                return n1 * t * t;
            }
            else if (t < 2f / d1)
            {
                return n1 * (t -= 1.5f / d1) * t + 0.75f;
            }
            else if (t < 2.5f / d1)
            {
                return n1 * (t -= 2.25f / d1) * t + 0.9375f;
            }
            else
            {
                return n1 * (t -= 2.625f / d1) * t + 0.984375f;
            }
        }
        
        #endregion
    }
}
