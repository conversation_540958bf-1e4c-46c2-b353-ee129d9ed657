Using pre-set license
Built from '6000.2/respin/6000.2.0b2-d4c40f50eb81' branch; Version is '6000.2.0b2 (2bdac9ac0d74) revision 2874057'; Using compiler version '*********'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'pt' Physical Memory: 7875 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-22T13:37:07Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.2.0b2\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Code Projects/Octopath Traveler/TacticalCombatSystem
-logFile
Logs/AssetImportWorker0.log
-srvPort
15453
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Code Projects/Octopath Traveler/TacticalCombatSystem
C:/Code Projects/Octopath Traveler/TacticalCombatSystem
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25564]  Target information:

Player connection [25564]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1172187757 [EditorId] 1172187757 [Version] 1048832 [Id] WindowsEditor(7,Andy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25564]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1172187757 [EditorId] 1172187757 [Version] 1048832 [Id] WindowsEditor(7,Andy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25564] Host joined multi-casting on [***********:54997]...
Player connection [25564] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 379.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.2.0b2 (2bdac9ac0d74)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Code Projects/Octopath Traveler/TacticalCombatSystem/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) UHD Graphics (ID=0xa7a9)
    Vendor:   Intel
    VRAM:     3937 MB
    Driver:   31.0.101.4502
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56444
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.023072 seconds.
- Loaded All Assemblies, in  3.941 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.792 seconds
Domain Reload Profiling: 4725ms
	BeginReloadAssembly (540ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (5ms)
	RebuildCommonClasses (141ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (337ms)
	LoadAllAssembliesAndSetupDomain (2887ms)
		LoadAssemblies (558ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2842ms)
			TypeCache.Refresh (2839ms)
				TypeCache.ScanAssembly (2812ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (546ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (79ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (103ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 27.262 seconds
Refreshing native plugins compatible for Editor in 5.77 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.316 seconds
Domain Reload Profiling: 29573ms
	BeginReloadAssembly (518ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (26589ms)
		LoadAssemblies (26175ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (745ms)
			TypeCache.Refresh (522ms)
				TypeCache.ScanAssembly (484ms)
			BuildScriptInfoCaches (196ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (2318ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2098ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (277ms)
			ProcessInitializeOnLoadAttributes (1582ms)
			ProcessInitializeOnLoadMethodAttributes (213ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 171 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (8.8 MB). Loaded Objects now: 8471.
Memory consumption went from 170.7 MB to 162.0 MB.
Total: 31.500600 ms (FindLiveObjects: 6.088600 ms CreateObjectMapping: 1.118600 ms MarkObjects: 15.415200 ms  DeleteObjects: 8.671500 ms)

========================================================================
Received Import Request.
  Time since last request: 150327.651167 seconds.
  path: Assets/Scripts/Battle/Core/BattleManager.cs
  artifactKey: Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Battle/Core/BattleManager.cs using Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6ebb689b3fb84ff7182bc5ece1daf9f') in 0.4476372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.002299 seconds.
  path: Assets/Scripts/Characters/BaseCharacter.cs
  artifactKey: Guid(9f3fca06ee1c1e946a591fba6ec73f5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/BaseCharacter.cs using Guid(9f3fca06ee1c1e946a591fba6ec73f5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39437b46c253755729ede857d7c9f5d1') in 0.0180256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7896 unused Assets / (5.7 MB). Loaded Objects now: 8473.
Memory consumption went from 138.2 MB to 132.5 MB.
Total: 292.063700 ms (FindLiveObjects: 3.812300 ms CreateObjectMapping: 0.861600 ms MarkObjects: 283.857200 ms  DeleteObjects: 3.284700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 17.839 seconds
Refreshing native plugins compatible for Editor in 3.12 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.768 seconds
Domain Reload Profiling: 19611ms
	BeginReloadAssembly (5364ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (644ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (8ms)
		CreateAndSetChildDomain (2069ms)
	RebuildCommonClasses (134ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (124ms)
	LoadAllAssembliesAndSetupDomain (12186ms)
		LoadAssemblies (2487ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10380ms)
			TypeCache.Refresh (171ms)
				TypeCache.ScanAssembly (10ms)
			BuildScriptInfoCaches (10127ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1772ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1527ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (289ms)
			ProcessInitializeOnLoadAttributes (890ms)
			ProcessInitializeOnLoadMethodAttributes (312ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (5ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 38 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7903 unused Assets / (3.9 MB). Loaded Objects now: 8476.
Memory consumption went from 142.0 MB to 138.1 MB.
Total: 36.797600 ms (FindLiveObjects: 2.946000 ms CreateObjectMapping: 0.722400 ms MarkObjects: 25.023000 ms  DeleteObjects: 7.754500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.785 seconds
Refreshing native plugins compatible for Editor in 4.84 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.918 seconds
Domain Reload Profiling: 12719ms
	BeginReloadAssembly (3175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (134ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (1822ms)
	RebuildCommonClasses (129ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (154ms)
	LoadAllAssembliesAndSetupDomain (4311ms)
		LoadAssemblies (3674ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1185ms)
			TypeCache.Refresh (76ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (1006ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (4919ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4591ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (418ms)
			ProcessInitializeOnLoadAttributes (1763ms)
			ProcessInitializeOnLoadMethodAttributes (2374ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7903 unused Assets / (6.7 MB). Loaded Objects now: 8478.
Memory consumption went from 142.0 MB to 135.3 MB.
Total: 24.913700 ms (FindLiveObjects: 0.792400 ms CreateObjectMapping: 1.034200 ms MarkObjects: 16.719200 ms  DeleteObjects: 6.348600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2459.826936 seconds.
  path: Assets/Scripts
  artifactKey: Guid(b578906aea569cc409f780a5a3f7d3be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts using Guid(b578906aea569cc409f780a5a3f7d3be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6d269cae207c132109e4c56e652826f') in 0.043631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.767634 seconds.
  path: Assets/Scripts/Characters
  artifactKey: Guid(9932525b510ff0849806ea96e023fad6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Characters using Guid(9932525b510ff0849806ea96e023fad6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5951a21c7d7c79a75aa64f5d6c8060f1') in 0.0015841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 50.631080 seconds.
  path: Assets/Prefabs/CharacterUI.prefab
  artifactKey: Guid(ce664b80f3de5f84f8301c58bada6187) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/CharacterUI.prefab using Guid(ce664b80f3de5f84f8301c58bada6187) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '028f72c26ca3e66ff0a43ded88ef9555') in 0.4893686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.396056 seconds.
  path: Assets/Prefabs/CharacterUI.prefab
  artifactKey: Guid(ce664b80f3de5f84f8301c58bada6187) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Prefabs/CharacterUI.prefab using Guid(ce664b80f3de5f84f8301c58bada6187) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd632029873d24d78700cf829b59678ae') in 0.5208676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.897 seconds
Refreshing native plugins compatible for Editor in 7.14 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.776 seconds
Domain Reload Profiling: 7711ms
	BeginReloadAssembly (608ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (258ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (54ms)
	LoadAllAssembliesAndSetupDomain (3196ms)
		LoadAssemblies (1524ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1881ms)
			TypeCache.Refresh (80ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (1664ms)
			ResolveRequiredComponents (85ms)
	FinalizeReload (3779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3048ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (447ms)
			ProcessInitializeOnLoadAttributes (2018ms)
			ProcessInitializeOnLoadMethodAttributes (531ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (6ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (6.4 MB). Loaded Objects now: 8483.
Memory consumption went from 142.3 MB to 135.9 MB.
Total: 31.826300 ms (FindLiveObjects: 3.451600 ms CreateObjectMapping: 1.412100 ms MarkObjects: 18.120000 ms  DeleteObjects: 8.833200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 10.038 seconds
Refreshing native plugins compatible for Editor in 2.75 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.883 seconds
Domain Reload Profiling: 12931ms
	BeginReloadAssembly (2592ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (124ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (1463ms)
	RebuildCommonClasses (207ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (149ms)
	LoadAllAssembliesAndSetupDomain (7065ms)
		LoadAssemblies (6054ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1403ms)
			TypeCache.Refresh (194ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (1122ms)
			ResolveRequiredComponents (33ms)
	FinalizeReload (2886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2616ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (342ms)
			ProcessInitializeOnLoadAttributes (1303ms)
			ProcessInitializeOnLoadMethodAttributes (940ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 5.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7903 unused Assets / (5.8 MB). Loaded Objects now: 8485.
Memory consumption went from 142.3 MB to 136.5 MB.
Total: 19.034100 ms (FindLiveObjects: 1.960700 ms CreateObjectMapping: 0.602100 ms MarkObjects: 12.123600 ms  DeleteObjects: 4.236200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.167 seconds
Refreshing native plugins compatible for Editor in 5.04 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.761 seconds
Domain Reload Profiling: 4934ms
	BeginReloadAssembly (412ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (1651ms)
		LoadAssemblies (1005ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (840ms)
			TypeCache.Refresh (61ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (719ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (2763ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2454ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (384ms)
			ProcessInitializeOnLoadAttributes (1586ms)
			ProcessInitializeOnLoadMethodAttributes (449ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 5.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7903 unused Assets / (6.6 MB). Loaded Objects now: 8487.
Memory consumption went from 142.2 MB to 135.6 MB.
Total: 34.061800 ms (FindLiveObjects: 4.283200 ms CreateObjectMapping: 1.881300 ms MarkObjects: 18.698900 ms  DeleteObjects: 9.193100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 703.404929 seconds.
  path: Assets/Scripts/Interfaces/Core/IBattleManager.cs
  artifactKey: Guid(f4a6963d087425248a693e47715f0331) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Interfaces/Core/IBattleManager.cs using Guid(f4a6963d087425248a693e47715f0331) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '167d9d0c66824e2a60dd8dd1a7cf7be6') in 5.2828144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.005535 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b255b064ab790f3c0179adf9e89f1360') in 0.0558951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 133.818 seconds
Refreshing native plugins compatible for Editor in 43.70 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.311 seconds
Domain Reload Profiling: 140203ms
	BeginReloadAssembly (86250ms)
		ExecutionOrderSort (4ms)
		DisableScriptedObjects (226ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (47ms)
		CreateAndSetChildDomain (24835ms)
	RebuildCommonClasses (288ms)
	RebuildNativeTypeToScriptingClass (70ms)
	initialDomainReloadingComplete (2773ms)
	LoadAllAssembliesAndSetupDomain (44500ms)
		LoadAssemblies (100786ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2835ms)
			TypeCache.Refresh (302ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (2347ms)
			ResolveRequiredComponents (74ms)
	FinalizeReload (6322ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (30ms)
			BeforeProcessingInitializeOnLoad (379ms)
			ProcessInitializeOnLoadAttributes (2184ms)
			ProcessInitializeOnLoadMethodAttributes (872ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (4ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7903 unused Assets / (7.6 MB). Loaded Objects now: 8489.
Memory consumption went from 142.3 MB to 134.6 MB.
Total: 34.943200 ms (FindLiveObjects: 5.615400 ms CreateObjectMapping: 1.132800 ms MarkObjects: 19.615300 ms  DeleteObjects: 8.340900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.520 seconds
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.270 seconds
Domain Reload Profiling: 11807ms
	BeginReloadAssembly (3053ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (266ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (996ms)
	RebuildCommonClasses (93ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (129ms)
	LoadAllAssembliesAndSetupDomain (6236ms)
		LoadAssemblies (5498ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1237ms)
			TypeCache.Refresh (82ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (1084ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (2271ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2045ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (375ms)
			ProcessInitializeOnLoadAttributes (1286ms)
			ProcessInitializeOnLoadMethodAttributes (360ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (4ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7903 unused Assets / (5.8 MB). Loaded Objects now: 8491.
Memory consumption went from 142.3 MB to 136.5 MB.
Total: 22.641600 ms (FindLiveObjects: 2.255000 ms CreateObjectMapping: 0.688000 ms MarkObjects: 13.875500 ms  DeleteObjects: 5.818200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (5.9 MB). Loaded Objects now: 8492.
Memory consumption went from 142.3 MB to 136.4 MB.
Total: 4690.724200 ms (FindLiveObjects: 10.199300 ms CreateObjectMapping: 2.695800 ms MarkObjects: 3382.570900 ms  DeleteObjects: 1295.249500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7051.607077 seconds.
  path: Assets/Scripts/Characters/Billboard.cs
  artifactKey: Guid(0efd041f6632e124ba2a86e577c3701f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/Billboard.cs using Guid(0efd041f6632e124ba2a86e577c3701f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00000000000000000000000000000000') in 4.498011 seconds
Import Error Code:(4)
Message: Build asset version error: assets/scripts/characters/billboard.cs in SourceAssetDB has modification time of '2025-06-22T16:28:45.4432706Z' while content on disk has modification time of '2025-06-22T16:28:47.2072833Z'
  ERROR: Build asset version error: assets/scripts/characters/billboard.cs in SourceAssetDB has modification time of '2025-06-22T16:28:45.4432706Z' while content on disk has modification time of '2025-06-22T16:28:47.2072833Z'
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (4.7 MB). Loaded Objects now: 8492.
Memory consumption went from 142.3 MB to 137.6 MB.
Total: 348.711800 ms (FindLiveObjects: 4.053700 ms CreateObjectMapping: 0.542400 ms MarkObjects: 340.583300 ms  DeleteObjects: 3.528400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 194.225979 seconds.
  path: Assets/Scripts/Characters/CharacterSpawner.cs
  artifactKey: Guid(8ab3bc2c71e55454f83511ecfd039a53) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/CharacterSpawner.cs using Guid(8ab3bc2c71e55454f83511ecfd039a53) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b141746b5fb725b8bb5acef487c28871') in 0.2118211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Editor/SceneSetupHelper.cs
  artifactKey: Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/SceneSetupHelper.cs using Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f75287f3030bc2360d6e0b53917693ab') in 0.0297001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000462 seconds.
  path: Assets/Scripts/Core/BattleInitializer.cs
  artifactKey: Guid(0e8651b41df39de40b6a427e7a8e23e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/BattleInitializer.cs using Guid(0e8651b41df39de40b6a427e7a8e23e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54829bc5d60bf2c1c747c4d5afdff9df') in 0.0289124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (6.6 MB). Loaded Objects now: 8492.
Memory consumption went from 142.3 MB to 135.7 MB.
Total: 307.898700 ms (FindLiveObjects: 11.988800 ms CreateObjectMapping: 1.850500 ms MarkObjects: 286.347600 ms  DeleteObjects: 7.708300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 198.764223 seconds.
  path: Assets/Scripts/Characters/Billboard.cs
  artifactKey: Guid(0efd041f6632e124ba2a86e577c3701f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/Billboard.cs using Guid(0efd041f6632e124ba2a86e577c3701f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '343f9d91711adac7baf5b81767e9f822') in 0.12503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 5.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (7.3 MB). Loaded Objects now: 8492.
Memory consumption went from 142.3 MB to 135.0 MB.
Total: 126.512900 ms (FindLiveObjects: 2.173100 ms CreateObjectMapping: 1.179500 ms MarkObjects: 118.039400 ms  DeleteObjects: 5.117600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 242.671764 seconds.
  path: Assets/Editor/PrefabSetupHelper.cs
  artifactKey: Guid(cb864343c522f9c49aaaa068841fc5b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/PrefabSetupHelper.cs using Guid(cb864343c522f9c49aaaa068841fc5b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a57c71665f1b503e048ce543f458a94') in 0.1164466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (5.5 MB). Loaded Objects now: 8492.
Memory consumption went from 142.3 MB to 136.8 MB.
Total: 181.849400 ms (FindLiveObjects: 3.695000 ms CreateObjectMapping: 0.490300 ms MarkObjects: 171.294300 ms  DeleteObjects: 6.366100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1264.621349 seconds.
  path: Assets/Scripts/Battle/Core/BattleManager.cs
  artifactKey: Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Battle/Core/BattleManager.cs using Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '499904c1b10e2765def164265fb20ceb') in 0.1274511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 10.302 seconds
Refreshing native plugins compatible for Editor in 2.31 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.821 seconds
Domain Reload Profiling: 12124ms
	BeginReloadAssembly (4424ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (359ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (8ms)
		CreateAndSetChildDomain (2017ms)
	RebuildCommonClasses (124ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (155ms)
	LoadAllAssembliesAndSetupDomain (5570ms)
		LoadAssemblies (4984ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1264ms)
			TypeCache.Refresh (207ms)
				TypeCache.ScanAssembly (17ms)
			BuildScriptInfoCaches (986ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1822ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1548ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (320ms)
			ProcessInitializeOnLoadAttributes (1009ms)
			ProcessInitializeOnLoadMethodAttributes (192ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (5.8 MB). Loaded Objects now: 8494.
Memory consumption went from 142.2 MB to 136.4 MB.
Total: 32.080600 ms (FindLiveObjects: 2.150800 ms CreateObjectMapping: 1.319700 ms MarkObjects: 15.279600 ms  DeleteObjects: 13.325600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 211.378680 seconds.
  path: Assets/Scripts/Core/BattleInitializer.cs
  artifactKey: Guid(0e8651b41df39de40b6a427e7a8e23e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/BattleInitializer.cs using Guid(0e8651b41df39de40b6a427e7a8e23e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef7e6d9b7afaebf810fc69970ad07b56') in 0.3195839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.140 seconds
Refreshing native plugins compatible for Editor in 3.59 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.372 seconds
Domain Reload Profiling: 7526ms
	BeginReloadAssembly (484ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (212ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (4563ms)
		LoadAssemblies (3086ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1459ms)
			TypeCache.Refresh (355ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (1026ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (2374ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2073ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (447ms)
			ProcessInitializeOnLoadAttributes (1281ms)
			ProcessInitializeOnLoadMethodAttributes (315ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (6.3 MB). Loaded Objects now: 8496.
Memory consumption went from 142.3 MB to 136.0 MB.
Total: 20.746800 ms (FindLiveObjects: 2.449500 ms CreateObjectMapping: 0.460000 ms MarkObjects: 10.933600 ms  DeleteObjects: 6.900000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (6.9 MB). Loaded Objects now: 8496.
Memory consumption went from 142.4 MB to 135.5 MB.
Total: 645.888500 ms (FindLiveObjects: 5.150300 ms CreateObjectMapping: 0.495300 ms MarkObjects: 629.669000 ms  DeleteObjects: 10.134000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1647.587146 seconds.
  path: Assets/Scripts/Battle/Core/BattleManager.cs
  artifactKey: Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Battle/Core/BattleManager.cs using Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a39332b675471254948270ab6f844345') in 3.0797644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (6.8 MB). Loaded Objects now: 8496.
Memory consumption went from 142.4 MB to 135.6 MB.
Total: 504.186100 ms (FindLiveObjects: 6.192100 ms CreateObjectMapping: 0.647400 ms MarkObjects: 490.113100 ms  DeleteObjects: 7.227700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 628.142254 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c3c95751f7652b3c0ce05573f91ee34c') in 0.1324383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (7.6 MB). Loaded Objects now: 8496.
Memory consumption went from 142.4 MB to 134.7 MB.
Total: 483.211600 ms (FindLiveObjects: 4.449500 ms CreateObjectMapping: 0.496300 ms MarkObjects: 468.013900 ms  DeleteObjects: 10.083500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 286.868605 seconds.
  path: Assets/Scripts/Battle/Core/BattleManager.cs
  artifactKey: Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Battle/Core/BattleManager.cs using Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '545a581d5d0497eeb8c08e4e27c29b32') in 0.1668272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7897 unused Assets / (7.8 MB). Loaded Objects now: 8496.
Memory consumption went from 142.4 MB to 134.5 MB.
Total: 510.207300 ms (FindLiveObjects: 5.506100 ms CreateObjectMapping: 0.611500 ms MarkObjects: 496.624100 ms  DeleteObjects: 7.461100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 108.778758 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69722125f15298bdcd2fb2e6214e8b0d') in 0.1062368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 20.730 seconds
Refreshing native plugins compatible for Editor in 3.35 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.391 seconds
Domain Reload Profiling: 25129ms
	BeginReloadAssembly (6765ms)
		ExecutionOrderSort (4ms)
		DisableScriptedObjects (407ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (9ms)
		CreateAndSetChildDomain (2945ms)
	RebuildCommonClasses (278ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (13587ms)
		LoadAssemblies (6525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (8441ms)
			TypeCache.Refresh (241ms)
				TypeCache.ScanAssembly (17ms)
			BuildScriptInfoCaches (8120ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (4394ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3984ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (33ms)
			BeforeProcessingInitializeOnLoad (458ms)
			ProcessInitializeOnLoadAttributes (2855ms)
			ProcessInitializeOnLoadMethodAttributes (601ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (6.7 MB). Loaded Objects now: 8498.
Memory consumption went from 142.3 MB to 135.7 MB.
Total: 37.343200 ms (FindLiveObjects: 4.206200 ms CreateObjectMapping: 0.793000 ms MarkObjects: 22.118200 ms  DeleteObjects: 9.923100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.703 seconds
Refreshing native plugins compatible for Editor in 2.07 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.327 seconds
Domain Reload Profiling: 3033ms
	BeginReloadAssembly (438ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (1183ms)
		LoadAssemblies (940ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (506ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (446ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1328ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1142ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (223ms)
			ProcessInitializeOnLoadAttributes (658ms)
			ProcessInitializeOnLoadMethodAttributes (245ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (8.8 MB). Loaded Objects now: 8500.
Memory consumption went from 142.3 MB to 133.4 MB.
Total: 14.276700 ms (FindLiveObjects: 1.390100 ms CreateObjectMapping: 0.532500 ms MarkObjects: 7.094400 ms  DeleteObjects: 5.256500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1947.659954 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc06514f0404efc886f759a9b4446bdf') in 0.4893288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 56.175129 seconds.
  path: Assets/Scenes/DynamicBattleScene.unity
  artifactKey: Guid(1d937aaefad21ae468cdd259342d7665) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/DynamicBattleScene.unity using Guid(1d937aaefad21ae468cdd259342d7665) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d9a61f5d1fac5000c3b1f0100594514') in 0.2503544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.249957 seconds.
  path: Assets/Scenes/ManualTestScene.unity
  artifactKey: Guid(65f82688c0bb51f4ab9557fa111bc585) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/ManualTestScene.unity using Guid(65f82688c0bb51f4ab9557fa111bc585) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dbd2025b2c21ff5aa478092d5d4ae2b9') in 0.030409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 55.927549 seconds.
  path: Assets/Scenes/ManualTestScene.unity
  artifactKey: Guid(65f82688c0bb51f4ab9557fa111bc585) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/ManualTestScene.unity using Guid(65f82688c0bb51f4ab9557fa111bc585) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2669dbf50823f96b0d2406a38f76896a') in 0.3065559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 38.418889 seconds.
  path: Assets/Scenes/DynamicBattleScene.unity
  artifactKey: Guid(1d937aaefad21ae468cdd259342d7665) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/DynamicBattleScene.unity using Guid(1d937aaefad21ae468cdd259342d7665) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6656d18f1ecc3c0be4f1881781f8ccd1') in 0.0158431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.722 seconds
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.228 seconds
Domain Reload Profiling: 9969ms
	BeginReloadAssembly (2050ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (121ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (1063ms)
	RebuildCommonClasses (102ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (5427ms)
		LoadAssemblies (3507ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2360ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (2070ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (2229ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1865ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (383ms)
			ProcessInitializeOnLoadAttributes (1162ms)
			ProcessInitializeOnLoadMethodAttributes (288ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (6.8 MB). Loaded Objects now: 8502.
Memory consumption went from 142.3 MB to 135.5 MB.
Total: 27.175700 ms (FindLiveObjects: 2.658600 ms CreateObjectMapping: 0.992400 ms MarkObjects: 15.010400 ms  DeleteObjects: 8.508600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 17.869 seconds
Refreshing native plugins compatible for Editor in 2.63 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.057 seconds
Domain Reload Profiling: 19931ms
	BeginReloadAssembly (12909ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (349ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (7ms)
		CreateAndSetChildDomain (10802ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (117ms)
	LoadAllAssembliesAndSetupDomain (4733ms)
		LoadAssemblies (4011ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1276ms)
			TypeCache.Refresh (97ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (1087ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (2059ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1781ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (297ms)
			ProcessInitializeOnLoadAttributes (1015ms)
			ProcessInitializeOnLoadMethodAttributes (438ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (8.2 MB). Loaded Objects now: 8504.
Memory consumption went from 142.2 MB to 134.1 MB.
Total: 26.839900 ms (FindLiveObjects: 2.304900 ms CreateObjectMapping: 1.637200 ms MarkObjects: 14.499300 ms  DeleteObjects: 8.394600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 501.898356 seconds.
  path: Assets/Editor/SceneSetupHelper.cs
  artifactKey: Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/SceneSetupHelper.cs using Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '59c22443724f0c2816f980581bfafe05') in 0.4596922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.671 seconds
Refreshing native plugins compatible for Editor in 4.58 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.650 seconds
Domain Reload Profiling: 11339ms
	BeginReloadAssembly (3679ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (282ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (9ms)
		CreateAndSetChildDomain (1975ms)
	RebuildCommonClasses (87ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (126ms)
	LoadAllAssembliesAndSetupDomain (4773ms)
		LoadAssemblies (3178ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2034ms)
			TypeCache.Refresh (115ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (1767ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (2651ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2334ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (449ms)
			ProcessInitializeOnLoadAttributes (1569ms)
			ProcessInitializeOnLoadMethodAttributes (279ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7904 unused Assets / (7.1 MB). Loaded Objects now: 8506.
Memory consumption went from 142.3 MB to 135.2 MB.
Total: 32.486900 ms (FindLiveObjects: 1.731200 ms CreateObjectMapping: 1.037100 ms MarkObjects: 18.309300 ms  DeleteObjects: 11.404300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7898 unused Assets / (6.4 MB). Loaded Objects now: 8507.
Memory consumption went from 142.4 MB to 136.0 MB.
Total: 43.847200 ms (FindLiveObjects: 2.118600 ms CreateObjectMapping: 2.620000 ms MarkObjects: 22.766600 ms  DeleteObjects: 16.338900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 406.097301 seconds.
  path: Assets/Editor/SceneSetupHelper.cs
  artifactKey: Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/SceneSetupHelper.cs using Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'caf5548064c750a0f6a4544d72e8b03a') in 40.8583882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7898 unused Assets / (12.2 MB). Loaded Objects now: 8507.
Memory consumption went from 142.4 MB to 130.2 MB.
Total: 155.313000 ms (FindLiveObjects: 7.949700 ms CreateObjectMapping: 0.561600 ms MarkObjects: 121.021800 ms  DeleteObjects: 25.496700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4.298950 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '054e9ebbaf9c4b9e5aaa8d5edda96ee9') in 4.5489325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.872 seconds
Refreshing native plugins compatible for Editor in 2.39 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.305 seconds
Domain Reload Profiling: 9183ms
	BeginReloadAssembly (3562ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (468ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (7ms)
		CreateAndSetChildDomain (1733ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (4181ms)
		LoadAssemblies (2828ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1695ms)
			TypeCache.Refresh (408ms)
				TypeCache.ScanAssembly (30ms)
			BuildScriptInfoCaches (1144ms)
			ResolveRequiredComponents (39ms)
	FinalizeReload (1306ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1129ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (230ms)
			ProcessInitializeOnLoadAttributes (680ms)
			ProcessInitializeOnLoadMethodAttributes (199ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (6.8 MB). Loaded Objects now: 8509.
Memory consumption went from 142.3 MB to 135.5 MB.
Total: 16.546400 ms (FindLiveObjects: 1.443700 ms CreateObjectMapping: 0.386700 ms MarkObjects: 10.003800 ms  DeleteObjects: 4.708000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.528 seconds
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.879 seconds
Domain Reload Profiling: 3408ms
	BeginReloadAssembly (335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (1083ms)
		LoadAssemblies (865ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (390ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (360ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1880ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1704ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (398ms)
			ProcessInitializeOnLoadAttributes (1010ms)
			ProcessInitializeOnLoadMethodAttributes (283ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (7.6 MB). Loaded Objects now: 8511.
Memory consumption went from 142.3 MB to 134.7 MB.
Total: 23.507700 ms (FindLiveObjects: 3.503100 ms CreateObjectMapping: 1.058600 ms MarkObjects: 9.880100 ms  DeleteObjects: 9.063400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 145.129456 seconds.
  path: Assets/Editor/SceneSetupHelper.cs
  artifactKey: Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/SceneSetupHelper.cs using Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'deab0f1082d157b861e3908de962aba0') in 0.3110302 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7898 unused Assets / (5.2 MB). Loaded Objects now: 8511.
Memory consumption went from 142.4 MB to 137.2 MB.
Total: 551.684700 ms (FindLiveObjects: 6.993800 ms CreateObjectMapping: 0.449100 ms MarkObjects: 533.849200 ms  DeleteObjects: 10.387100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 146.402623 seconds.
  path: Assets/Editor/SceneSetupHelper.cs
  artifactKey: Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Editor/SceneSetupHelper.cs using Guid(48a0bbadd708c03449d9ace5a0cef02e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6a13e13976b92e7b32b38c41670ed91') in 0.1741072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Scripts/Core/BattleInputHandler.cs
  artifactKey: Guid(432475ff3d058e84aa82ca40cd1c17b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/BattleInputHandler.cs using Guid(432475ff3d058e84aa82ca40cd1c17b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c56125b85717d693e050ec849ee6350c') in 0.0433174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2860925234b0a681aca9ffc9c9caced2') in 0.0314896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 9.880962 seconds.
  path: Assets/Scenes/ManualTestScene.unity
  artifactKey: Guid(65f82688c0bb51f4ab9557fa111bc585) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/ManualTestScene.unity using Guid(65f82688c0bb51f4ab9557fa111bc585) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '301a541393a322723899b591b752401c') in 0.0041574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.480 seconds
Refreshing native plugins compatible for Editor in 4.40 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.320 seconds
Domain Reload Profiling: 8809ms
	BeginReloadAssembly (3588ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (302ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (6ms)
		CreateAndSetChildDomain (1752ms)
	RebuildCommonClasses (93ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (119ms)
	LoadAllAssembliesAndSetupDomain (3669ms)
		LoadAssemblies (2259ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2001ms)
			TypeCache.Refresh (207ms)
				TypeCache.ScanAssembly (13ms)
			BuildScriptInfoCaches (1559ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1321ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1138ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (218ms)
			ProcessInitializeOnLoadAttributes (716ms)
			ProcessInitializeOnLoadMethodAttributes (183ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (6.5 MB). Loaded Objects now: 8513.
Memory consumption went from 142.4 MB to 135.9 MB.
Total: 16.977800 ms (FindLiveObjects: 1.422100 ms CreateObjectMapping: 0.491200 ms MarkObjects: 10.621800 ms  DeleteObjects: 4.440300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.863 seconds
Refreshing native plugins compatible for Editor in 2.16 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.070 seconds
Domain Reload Profiling: 5935ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (3403ms)
		LoadAssemblies (663ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2926ms)
			TypeCache.Refresh (268ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (2574ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (2070ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1809ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (376ms)
			ProcessInitializeOnLoadAttributes (1021ms)
			ProcessInitializeOnLoadMethodAttributes (384ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (9.0 MB). Loaded Objects now: 8515.
Memory consumption went from 142.4 MB to 133.4 MB.
Total: 28.192300 ms (FindLiveObjects: 2.234700 ms CreateObjectMapping: 1.688100 ms MarkObjects: 12.513300 ms  DeleteObjects: 11.750600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 207.963907 seconds.
  path: Assets/Scripts/Interfaces/Core/IBattleManager.cs
  artifactKey: Guid(f4a6963d087425248a693e47715f0331) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Interfaces/Core/IBattleManager.cs using Guid(f4a6963d087425248a693e47715f0331) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '352be4835155402e890409232bbfa66c') in 0.292618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.763 seconds
Refreshing native plugins compatible for Editor in 3.12 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.375 seconds
Domain Reload Profiling: 8143ms
	BeginReloadAssembly (3430ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (321ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (11ms)
		CreateAndSetChildDomain (1525ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (88ms)
	LoadAllAssembliesAndSetupDomain (3175ms)
		LoadAssemblies (2179ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1389ms)
			TypeCache.Refresh (214ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (922ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1376ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1179ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (265ms)
			ProcessInitializeOnLoadAttributes (699ms)
			ProcessInitializeOnLoadMethodAttributes (194ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (6.8 MB). Loaded Objects now: 8517.
Memory consumption went from 142.4 MB to 135.5 MB.
Total: 16.274900 ms (FindLiveObjects: 1.189500 ms CreateObjectMapping: 0.411800 ms MarkObjects: 9.940300 ms  DeleteObjects: 4.729100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 133.000209 seconds.
  path: Assets/Scripts/Core/BattleInitializer.cs
  artifactKey: Guid(0e8651b41df39de40b6a427e7a8e23e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/BattleInitializer.cs using Guid(0e8651b41df39de40b6a427e7a8e23e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa99b62d7fa9fe280cbde62c59b803a3') in 0.3024202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.217 seconds
Refreshing native plugins compatible for Editor in 1.04 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.292 seconds
Domain Reload Profiling: 3507ms
	BeginReloadAssembly (621ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (235ms)
	RebuildCommonClasses (106ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (1407ms)
		LoadAssemblies (1207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (479ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (356ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1293ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1106ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (320ms)
			ProcessInitializeOnLoadAttributes (632ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (6.9 MB). Loaded Objects now: 8519.
Memory consumption went from 142.4 MB to 135.5 MB.
Total: 27.344400 ms (FindLiveObjects: 2.908800 ms CreateObjectMapping: 1.325800 ms MarkObjects: 17.278200 ms  DeleteObjects: 5.827600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.677 seconds
Refreshing native plugins compatible for Editor in 2.95 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.549 seconds
Domain Reload Profiling: 13235ms
	BeginReloadAssembly (2147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (146ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (1066ms)
	RebuildCommonClasses (103ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (4283ms)
		LoadAssemblies (3653ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1077ms)
			TypeCache.Refresh (67ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (898ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (6552ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6191ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (574ms)
			ProcessInitializeOnLoadAttributes (4825ms)
			ProcessInitializeOnLoadMethodAttributes (758ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (28ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (7.4 MB). Loaded Objects now: 8521.
Memory consumption went from 142.3 MB to 134.9 MB.
Total: 21.492600 ms (FindLiveObjects: 2.154300 ms CreateObjectMapping: 1.197200 ms MarkObjects: 11.077400 ms  DeleteObjects: 7.058500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.298 seconds
Refreshing native plugins compatible for Editor in 2.30 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.634 seconds
Domain Reload Profiling: 5939ms
	BeginReloadAssembly (919ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (425ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (3244ms)
		LoadAssemblies (2466ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1141ms)
			TypeCache.Refresh (80ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (961ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1635ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (274ms)
			ProcessInitializeOnLoadAttributes (897ms)
			ProcessInitializeOnLoadMethodAttributes (250ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (6.1 MB). Loaded Objects now: 8523.
Memory consumption went from 142.4 MB to 136.2 MB.
Total: 12.427200 ms (FindLiveObjects: 1.301100 ms CreateObjectMapping: 0.712200 ms MarkObjects: 6.764800 ms  DeleteObjects: 3.646100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.498 seconds
Refreshing native plugins compatible for Editor in 2.48 ms, found 3 plugins.
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.137 seconds
Domain Reload Profiling: 3637ms
	BeginReloadAssembly (298ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (1086ms)
		LoadAssemblies (685ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (529ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (487ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (2138ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1816ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (361ms)
			ProcessInitializeOnLoadAttributes (1109ms)
			ProcessInitializeOnLoadMethodAttributes (312ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7905 unused Assets / (8.5 MB). Loaded Objects now: 8525.
Memory consumption went from 142.4 MB to 134.0 MB.
Total: 24.423500 ms (FindLiveObjects: 2.031600 ms CreateObjectMapping: 0.843900 ms MarkObjects: 10.803600 ms  DeleteObjects: 10.740100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7898 unused Assets / (9.8 MB). Loaded Objects now: 8525.
Memory consumption went from 142.5 MB to 132.7 MB.
Total: 213.118900 ms (FindLiveObjects: 2.250000 ms CreateObjectMapping: 0.407300 ms MarkObjects: 202.868200 ms  DeleteObjects: 7.589800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7902 unused Assets / (5.3 MB). Loaded Objects now: 8529.
Memory consumption went from 142.4 MB to 137.1 MB.
Total: 224.285400 ms (FindLiveObjects: 5.255500 ms CreateObjectMapping: 1.603500 ms MarkObjects: 206.243100 ms  DeleteObjects: 11.175900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 9165.252916 seconds.
  path: Assets/Scripts/Characters/Character.cs
  artifactKey: Guid(4fce694c145870a499e3db7da3479392) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/Character.cs using Guid(4fce694c145870a499e3db7da3479392) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f4d5a4d8a23a8667d2a49614b873526') in 2.1605714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Scripts/Actions/ItemAction.cs
  artifactKey: Guid(75a8fd7ca715059438fa6e9bc2c32c0c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Actions/ItemAction.cs using Guid(75a8fd7ca715059438fa6e9bc2c32c0c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '281d4f016acb7f29945e2b40d080b145') in 0.0378095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000135 seconds.
  path: Assets/Scripts/Characters/Equipment.cs
  artifactKey: Guid(769e051cc349d9543a8fd07665b90050) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/Equipment.cs using Guid(769e051cc349d9543a8fd07665b90050) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b09856629caf7ba9b168cc30000fbed7') in 0.0394496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Data/Equipment
  artifactKey: Guid(7e7fc23702ac79b438c358234286614f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Data/Equipment using Guid(7e7fc23702ac79b438c358234286614f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '056746b475f014110b8866e9554bb61b') in 0.0365989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000883 seconds.
  path: Assets/Scripts/Actions/SkillAction.cs
  artifactKey: Guid(4f27e2d759918ad428c8d810f0d57db3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Actions/SkillAction.cs using Guid(4f27e2d759918ad428c8d810f0d57db3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e02ca1565adc92b75a5eb8d4c0e7f142') in 0.0425555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.003269 seconds.
  path: Assets/Data/StatusEffects
  artifactKey: Guid(4bfe18ba88bca1b429a40ab6e15d5c81) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Data/StatusEffects using Guid(4bfe18ba88bca1b429a40ab6e15d5c81) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de2636d624d75aa0dd84e9ea42d62d6a') in 0.1295576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000787 seconds.
  path: Assets/Scripts/Characters/StatType.cs
  artifactKey: Guid(736dcb0c043458c459800f379e798db5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/StatType.cs using Guid(736dcb0c043458c459800f379e798db5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '36a01cfa294815587fb94f9a44192ba0') in 0.0354683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7911 unused Assets / (8.0 MB). Loaded Objects now: 8538.
Memory consumption went from 142.5 MB to 134.5 MB.
Total: 464.704100 ms (FindLiveObjects: 2.968800 ms CreateObjectMapping: 1.262100 ms MarkObjects: 453.392500 ms  DeleteObjects: 6.815500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1451.642616 seconds.
  path: Assets/Scripts/Core/TurnOrderSystem.cs
  artifactKey: Guid(41fd7748a732bee4784ded8fa81955ea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/TurnOrderSystem.cs using Guid(41fd7748a732bee4784ded8fa81955ea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be0dfd5dc8e877fb3ab9bb1e94c696a1') in 0.1399514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Scripts/Core/TargetingSystem.cs
  artifactKey: Guid(c51d6903599d92c45bd699c902f21192) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/TargetingSystem.cs using Guid(c51d6903599d92c45bd699c902f21192) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64dff1421284a6a31d2c99ac7f05e4b4') in 0.0268313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000454 seconds.
  path: Assets/Scripts/UI/TargetingIndicator.cs
  artifactKey: Guid(b6c6606fbf459ab45b59f7b129351f3e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/TargetingIndicator.cs using Guid(b6c6606fbf459ab45b59f7b129351f3e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '200102396b0972d081bac4c5360d33fe') in 0.0348125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000780 seconds.
  path: Assets/Scripts/Core/CombatAction.cs
  artifactKey: Guid(5011390a78004b44091154c5b2579eb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/CombatAction.cs using Guid(5011390a78004b44091154c5b2579eb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d138d6d47d300676dc829c35bf1295b') in 0.0330468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000473 seconds.
  path: Assets/Scripts/AI/AIBehaviors.cs
  artifactKey: Guid(e5845efcd584d3643829b56ad839b925) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/AI/AIBehaviors.cs using Guid(e5845efcd584d3643829b56ad839b925) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3565788f13ca6daea141aa51d343537f') in 0.0237404 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Scripts/Core/DamageSystem.cs
  artifactKey: Guid(8d6955ed7edb09a41918a41674d2efff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/DamageSystem.cs using Guid(8d6955ed7edb09a41918a41674d2efff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc923734f18aef5b873576df80938404') in 0.0293532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000464 seconds.
  path: Assets/Scripts/UI/BattleUI.cs
  artifactKey: Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/BattleUI.cs using Guid(22864217bce74e443ba9c1018edc47af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64e7f33ac645816630221e8772676487') in 0.0741059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000686 seconds.
  path: Assets/Scripts/AI/CombatAI.cs
  artifactKey: Guid(f6db1339b2fdc934fb05877cf5b289ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/AI/CombatAI.cs using Guid(f6db1339b2fdc934fb05877cf5b289ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d19b32d910a6a38e3864d2ae96ba3c3') in 0.0372014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 10.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7912 unused Assets / (6.8 MB). Loaded Objects now: 8539.
Memory consumption went from 142.5 MB to 135.8 MB.
Total: 203.088100 ms (FindLiveObjects: 3.987500 ms CreateObjectMapping: 1.723100 ms MarkObjects: 191.053500 ms  DeleteObjects: 6.066900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1632.411085 seconds.
  path: Assets/Scripts/UI/DamageNumber.cs
  artifactKey: Guid(e0771a0d81a722a4a8dbb6c3717c7f87) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/UI/DamageNumber.cs using Guid(e0771a0d81a722a4a8dbb6c3717c7f87) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb5c6273862fc83646f27dd7d34300ad') in 0.1039901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Scripts/Core/CombatAction.cs
  artifactKey: Guid(5011390a78004b44091154c5b2579eb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/CombatAction.cs using Guid(5011390a78004b44091154c5b2579eb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b1501ff2dc5aef4d3ffa3dc965df594') in 0.0375759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Scripts/AI/AIBehaviors.cs
  artifactKey: Guid(e5845efcd584d3643829b56ad839b925) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/AI/AIBehaviors.cs using Guid(e5845efcd584d3643829b56ad839b925) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcc70c86f37f968adc797a1a9f743e7b') in 0.0310215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000706 seconds.
  path: Assets/Scripts/AI/CombatAI.cs
  artifactKey: Guid(f6db1339b2fdc934fb05877cf5b289ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/AI/CombatAI.cs using Guid(f6db1339b2fdc934fb05877cf5b289ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d2c703b746736f7c54b56305ac40f94') in 0.0303691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7912 unused Assets / (6.6 MB). Loaded Objects now: 8539.
Memory consumption went from 142.5 MB to 135.9 MB.
Total: 7204.263700 ms (FindLiveObjects: 2.523200 ms CreateObjectMapping: 0.445900 ms MarkObjects: 7194.287600 ms  DeleteObjects: 7.001600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 675.105075 seconds.
  path: Assets/Scripts/Core/ActionCategory.cs
  artifactKey: Guid(33c49a638c9a0454fafbc2dfaffdd3a8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/ActionCategory.cs using Guid(33c49a638c9a0454fafbc2dfaffdd3a8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '710c1cb33f8f7748f9878015264b4937') in 0.1171793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 9.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7911 unused Assets / (6.6 MB). Loaded Objects now: 8538.
Memory consumption went from 142.6 MB to 135.9 MB.
Total: 380.434600 ms (FindLiveObjects: 2.292300 ms CreateObjectMapping: 0.471200 ms MarkObjects: 369.714100 ms  DeleteObjects: 7.953100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1604.470089 seconds.
  path: Assets/Scripts/Core/CombatAction.cs
  artifactKey: Guid(5011390a78004b44091154c5b2579eb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/CombatAction.cs using Guid(5011390a78004b44091154c5b2579eb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e759fb786b86758fd500f12011266ca3') in 0.1349131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000162 seconds.
  path: Assets/Scripts/Core/DamageSystem.cs
  artifactKey: Guid(8d6955ed7edb09a41918a41674d2efff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/DamageSystem.cs using Guid(8d6955ed7edb09a41918a41674d2efff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5c2bd9f8410f07753f45ed389224668') in 0.062802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000911 seconds.
  path: Assets/Scripts/Abilities/AbilityEffect.cs
  artifactKey: Guid(9977c91c17bfb1243a0a2761263ab8bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Abilities/AbilityEffect.cs using Guid(9977c91c17bfb1243a0a2761263ab8bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe958fbab11558e18dad51be4d81d750') in 0.0908786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 7.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7911 unused Assets / (7.6 MB). Loaded Objects now: 8538.
Memory consumption went from 142.5 MB to 134.9 MB.
Total: 365.916800 ms (FindLiveObjects: 7.492600 ms CreateObjectMapping: 1.417700 ms MarkObjects: 349.001400 ms  DeleteObjects: 7.696000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 602.002650 seconds.
  path: Assets/Scripts/Core/TargetingSystem.cs
  artifactKey: Guid(c51d6903599d92c45bd699c902f21192) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/TargetingSystem.cs using Guid(c51d6903599d92c45bd699c902f21192) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e773e5b6438c2ff84a65ba9054585fdd') in 0.0947301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Scripts/Core/StatusEffect.cs
  artifactKey: Guid(97ff2677fcb0d9841a03aee10633c414) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/StatusEffect.cs using Guid(97ff2677fcb0d9841a03aee10633c414) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff21f448d2b1ad77130b1d906fa28b2e') in 0.0312211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000365 seconds.
  path: Assets/Scripts/Characters/BaseCharacter.cs
  artifactKey: Guid(9f3fca06ee1c1e946a591fba6ec73f5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Characters/BaseCharacter.cs using Guid(9f3fca06ee1c1e946a591fba6ec73f5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35f6442d0816b6343e6ea3d81bd2b7ca') in 0.0319109 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Scripts/Core/BattleInputHandler.cs
  artifactKey: Guid(432475ff3d058e84aa82ca40cd1c17b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/BattleInputHandler.cs using Guid(432475ff3d058e84aa82ca40cd1c17b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad1dbafaba7e7a886a694ad57463749f') in 0.0327938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000431 seconds.
  path: Assets/Scripts/AI/CombatAI.cs
  artifactKey: Guid(f6db1339b2fdc934fb05877cf5b289ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/AI/CombatAI.cs using Guid(f6db1339b2fdc934fb05877cf5b289ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56ad8548c174766efb57bd0c154381f6') in 0.0376652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7911 unused Assets / (8.6 MB). Loaded Objects now: 8538.
Memory consumption went from 142.5 MB to 133.9 MB.
Total: 119.449900 ms (FindLiveObjects: 2.591300 ms CreateObjectMapping: 0.445200 ms MarkObjects: 108.801500 ms  DeleteObjects: 7.607400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1317.616573 seconds.
  path: Assets/Scripts/Battle/Core/BattleManager.cs
  artifactKey: Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Battle/Core/BattleManager.cs using Guid(b9a4f19e67f78304f8d36094afa6920a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '020b27a82dff7f32bf2a9d3b5168a273') in 0.0883319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000182 seconds.
  path: Assets/Scripts/Core/StatusEffect.cs
  artifactKey: Guid(97ff2677fcb0d9841a03aee10633c414) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Core/StatusEffect.cs using Guid(97ff2677fcb0d9841a03aee10633c414) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c18ced0587ed0b71920fce8dc6dca47') in 0.0298466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7911 unused Assets / (8.7 MB). Loaded Objects now: 8538.
Memory consumption went from 142.5 MB to 133.9 MB.
Total: 202.844700 ms (FindLiveObjects: 2.333800 ms CreateObjectMapping: 0.413600 ms MarkObjects: 191.968100 ms  DeleteObjects: 8.124600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 70.618781 seconds.
  path: Assets/Scripts/Abilities/AbilityEffect.cs
  artifactKey: Guid(9977c91c17bfb1243a0a2761263ab8bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/Abilities/AbilityEffect.cs using Guid(9977c91c17bfb1243a0a2761263ab8bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '154f45b7c4d4282807a506ba4dd17d51') in 0.070985 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 23.593 seconds
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.982 seconds
Domain Reload Profiling: 25597ms
	BeginReloadAssembly (9694ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (436ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (9ms)
		CreateAndSetChildDomain (6286ms)
	RebuildCommonClasses (284ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (95ms)
	LoadAllAssembliesAndSetupDomain (13524ms)
		LoadAssemblies (9722ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (4713ms)
			TypeCache.Refresh (252ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (4340ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1984ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1715ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (367ms)
			ProcessInitializeOnLoadAttributes (946ms)
			ProcessInitializeOnLoadMethodAttributes (362ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (3ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 2.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7917 unused Assets / (6.8 MB). Loaded Objects now: 8537.
Memory consumption went from 142.3 MB to 135.5 MB.
Total: 25.807900 ms (FindLiveObjects: 2.443800 ms CreateObjectMapping: 1.305200 ms MarkObjects: 15.792000 ms  DeleteObjects: 6.261800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.076 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.296 seconds
Domain Reload Profiling: 5375ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (3611ms)
		LoadAssemblies (1601ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1232ms)
			TypeCache.Refresh (727ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (433ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1297ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1120ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (234ms)
			ProcessInitializeOnLoadAttributes (693ms)
			ProcessInitializeOnLoadMethodAttributes (171ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7916 unused Assets / (10.5 MB). Loaded Objects now: 8539.
Memory consumption went from 142.3 MB to 131.8 MB.
Total: 67.291200 ms (FindLiveObjects: 5.181900 ms CreateObjectMapping: 0.718200 ms MarkObjects: 36.525400 ms  DeleteObjects: 24.859500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.880 seconds
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.182 seconds
Domain Reload Profiling: 4065ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (133ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (1436ms)
		LoadAssemblies (992ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (580ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (469ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (2183ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1888ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (348ms)
			ProcessInitializeOnLoadAttributes (1245ms)
			ProcessInitializeOnLoadMethodAttributes (268ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 3.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7916 unused Assets / (7.9 MB). Loaded Objects now: 8541.
Memory consumption went from 142.3 MB to 134.4 MB.
Total: 28.759400 ms (FindLiveObjects: 3.352500 ms CreateObjectMapping: 1.151500 ms MarkObjects: 13.020700 ms  DeleteObjects: 11.229900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7910 unused Assets / (6.5 MB). Loaded Objects now: 8542.
Memory consumption went from 142.5 MB to 136.0 MB.
Total: 663.605300 ms (FindLiveObjects: 2.887000 ms CreateObjectMapping: 0.578700 ms MarkObjects: 650.899500 ms  DeleteObjects: 9.235000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1222.242263 seconds.
  path: Assets/UI/Combat/CombatUIView.cs
  artifactKey: Guid(d53a5771fce1ff8448a1fccf071268b4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI/Combat/CombatUIView.cs using Guid(d53a5771fce1ff8448a1fccf071268b4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'adf05263c53f44ed413045c7a92777c8') in 19.705503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 6.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7911 unused Assets / (6.6 MB). Loaded Objects now: 8543.
Memory consumption went from 142.5 MB to 135.9 MB.
Total: 2059.859500 ms (FindLiveObjects: 5.159200 ms CreateObjectMapping: 0.575900 ms MarkObjects: 2045.619700 ms  DeleteObjects: 8.500000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 66.938812 seconds.
  path: Assets/UI/Combat/PartyMemberStatus.uxml
  artifactKey: Guid(930ffc7d23ce3f44f81acf1ac8307c04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI/Combat/PartyMemberStatus.uxml using Guid(930ffc7d23ce3f44f81acf1ac8307c04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f264eab2d5f9e314064a829415b76a8') in 7.1238029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/UI/Combat/CombatUI.uss
  artifactKey: Guid(cd4dbe346cf27984083a8fe5fcd6e5fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UI/Combat/CombatUI.uss using Guid(cd4dbe346cf27984083a8fe5fcd6e5fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a2b64f2b97fb665146ed863eeeffc9f') in 0.1484809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 4.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7910 unused Assets / (10.9 MB). Loaded Objects now: 8542.
Memory consumption went from 142.9 MB to 132.0 MB.
Total: 4591.396000 ms (FindLiveObjects: 131.909500 ms CreateObjectMapping: 7.863900 ms MarkObjects: 3065.676200 ms  DeleteObjects: 1385.938900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 46.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7909 unused Assets / (6.8 MB). Loaded Objects now: 8541.
Memory consumption went from 142.9 MB to 136.0 MB.
Total: 1081.863600 ms (FindLiveObjects: 166.201300 ms CreateObjectMapping: 6.174300 ms MarkObjects: 838.059500 ms  DeleteObjects: 70.920900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Default GameObject Tag: Player already registered
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 11.500 seconds
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@ab3329688005/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <748d46004b1941a4ae235b187efb7b4d>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@ab3329688005\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.275 seconds
Domain Reload Profiling: 12783ms
	BeginReloadAssembly (3191ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (236ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (7ms)
		CreateAndSetChildDomain (1314ms)
	RebuildCommonClasses (304ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (7934ms)
		LoadAssemblies (7019ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1781ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (1608ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1276ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1102ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (253ms)
			ProcessInitializeOnLoadAttributes (702ms)
			ProcessInitializeOnLoadMethodAttributes (125ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Universal Render Pipeline/Terrain/Lit (Base Pass)' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/Terrain/Lit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 37 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7916 unused Assets / (7.3 MB). Loaded Objects now: 8543.
Memory consumption went from 142.4 MB to 135.1 MB.
Total: 15.724100 ms (FindLiveObjects: 0.645800 ms CreateObjectMapping: 0.608300 ms MarkObjects: 9.473300 ms  DeleteObjects: 4.993000 ms)

Prepare: number of updated asset objects reloaded= 0
