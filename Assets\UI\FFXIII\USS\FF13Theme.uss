/* Final Fantasy XIII Theme Stylesheet */

/* Color Variables (approximated in USS) */
:root {
    --primary-dark: rgb(26, 58, 110);      /* #1a3a6e */
    --primary-light: rgb(74, 144, 226);    /* #4a90e2 */
    --accent-blue: rgb(106, 176, 255);     /* #6ab0ff */
    --health-green: rgb(76, 175, 80);      /* #4CAF50 */
    --mana-blue: rgb(33, 150, 243);        /* #2196F3 */
    --warning-orange: rgb(255, 152, 0);    /* #FF9800 */
    --danger-red: rgb(244, 67, 54);        /* #F44336 */
    --background-dark: rgba(0, 0, 0, 0.8);
    --background-medium: rgba(0, 0, 0, 0.6);
    --background-light: rgba(0, 0, 0, 0.4);
    --text-primary: rgb(255, 255, 255);
    --text-secondary: rgb(204, 204, 204);
    --glow-blue: rgba(106, 176, 255, 0.5);
}

/* Root Container */
.ff13-battle-ui-root {
    flex-grow: 1;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    color: rgb(255, 255, 255);
    font-size: 18px;
}

/* Action Queue (Top) */
.action-queue-container {
    position: absolute;
    top: 20px;
    left: 50%;
    translate: -50% 0;
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 5px;
    padding: 10px;
    min-width: 400px;
}

.action-queue-title {
    color: rgb(255, 255, 255);
    font-size: 16px;
    margin-bottom: 5px;
    -unity-text-align: middle-center;
}

.action-queue-timeline {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 40px;
}

/* Battle Area (Center) */
.battle-area {
    flex-grow: 1;
    position: relative;
}

/* Bottom UI Container */
.bottom-ui-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    padding: 20px;
    height: 200px;
}

/* ATB Gauges (Bottom Left) */
.atb-container {
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 5px;
    padding: 10px;
    min-width: 250px;
}

.atb-title {
    color: rgb(255, 255, 255);
    font-size: 16px;
    margin-bottom: 10px;
    -unity-text-align: middle-center;
}

.atb-gauges {
    flex-direction: column;
}

.atb-gauge {
    flex-direction: row;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.atb-character-name {
    color: rgb(255, 255, 255);
    font-size: 14px;
    width: 80px;
    margin-right: 10px;
}

.atb-segments {
    flex-direction: row;
    height: 16px;
    width: 120px;
    background-color: rgb(50, 50, 50);
    border-radius: 8px;
    overflow: hidden;
}

.atb-segment {
    flex-grow: 1;
    margin-right: 2px;
    background-color: rgb(30, 30, 30);
    border-radius: 2px;
}

.atb-segment.filled {
    background-color: rgb(106, 176, 255);
}

.atb-segment.charging {
    background-color: rgb(74, 144, 226);
}

/* Command Menu (Bottom Center) */
.command-menu-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    translate: -50% 0;
    background-color: rgba(0, 0, 0, 0.8);
    border: 2px solid rgb(74, 144, 226);
    border-radius: 5px;
    padding: 10px;
    min-width: 200px;
}

.main-menu {
    flex-direction: column;
}

.menu-button {
    margin: 3px 0;
    padding: 8px 15px;
    background-color: rgb(26, 58, 110);
    color: rgb(255, 255, 255);
    border-width: 1px;
    border-color: rgb(74, 144, 226);
    border-radius: 3px;
    -unity-text-align: middle-left;
    font-size: 16px;
}

.menu-button:hover {
    background-color: rgb(42, 90, 158);
    border-color: rgb(106, 176, 255);
}

.menu-button:focus {
    background-color: rgb(42, 90, 158);
    border-color: rgb(106, 176, 255);
}

.submenu {
    flex-direction: column;
    min-width: 250px;
}

.submenu-title {
    color: rgb(255, 255, 255);
    font-size: 18px;
    margin-bottom: 10px;
    -unity-text-align: middle-center;
}

.action-list {
    max-height: 150px;
    margin-bottom: 10px;
}

.action-button {
    margin: 2px 0;
    padding: 6px 12px;
    background-color: rgb(26, 58, 110);
    color: rgb(255, 255, 255);
    border-width: 1px;
    border-color: rgb(74, 144, 226);
    border-radius: 3px;
    -unity-text-align: middle-left;
    font-size: 14px;
}

.action-button:hover {
    background-color: rgb(42, 90, 158);
    border-color: rgb(106, 176, 255);
}

.back-button {
    padding: 6px 12px;
    background-color: rgb(50, 50, 50);
    color: rgb(255, 255, 255);
    border-width: 1px;
    border-color: rgb(100, 100, 100);
    border-radius: 3px;
    -unity-text-align: middle-center;
    font-size: 14px;
}

.back-button:hover {
    background-color: rgb(70, 70, 70);
    border-color: rgb(150, 150, 150);
}

/* Paradigm System */
.paradigm-list {
    flex-direction: column;
    margin-bottom: 10px;
}

.paradigm-option {
    flex-direction: row;
    align-items: center;
    padding: 8px;
    margin: 2px 0;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    border-width: 1px;
    border-color: rgb(74, 144, 226);
}

.paradigm-option:hover {
    background-color: rgba(74, 144, 226, 0.3);
    border-color: rgb(106, 176, 255);
}

.paradigm-roles {
    flex-direction: row;
    flex-grow: 1;
}

.paradigm-role {
    margin-right: 10px;
    padding: 4px 8px;
    background-color: rgb(26, 58, 110);
    border-radius: 2px;
    font-size: 12px;
    color: rgb(255, 255, 255);
}

/* Character Status Panel (Right) */
.character-status-container {
    position: absolute;
    top: 20px;
    right: 20px;
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 5px;
    padding: 10px;
    min-width: 300px;
    max-height: 400px;
}

.party-title {
    color: rgb(255, 255, 255);
    font-size: 16px;
    margin-bottom: 10px;
    -unity-text-align: middle-center;
}

.party-members {
    flex-direction: column;
    margin-bottom: 15px;
}

.character-card {
    flex-direction: column;
    padding: 8px;
    margin-bottom: 8px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 3px;
    border-width: 1px;
    border-color: rgba(74, 144, 226, 0.5);
}

.character-card.active {
    border-color: rgb(106, 176, 255);
    background-color: rgba(106, 176, 255, 0.1);
}

.character-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.character-name {
    color: rgb(255, 255, 255);
    font-size: 16px;
    font-weight: bold;
}

.character-level {
    color: rgb(204, 204, 204);
    font-size: 12px;
}

.character-bars {
    flex-direction: column;
}

.health-bar-container {
    flex-direction: row;
    align-items: center;
    margin-bottom: 3px;
}

.health-label {
    color: rgb(255, 255, 255);
    font-size: 12px;
    width: 25px;
    margin-right: 5px;
}

.health-bar {
    width: 120px;
    height: 12px;
    background-color: rgb(50, 50, 50);
    border-radius: 6px;
    margin-right: 5px;
}

.health-fill {
    height: 100%;
    background-color: rgb(76, 175, 80);
    border-radius: 6px;
    transition: width 0.3s ease-in-out;
}

.health-text {
    color: rgb(255, 255, 255);
    font-size: 10px;
    min-width: 40px;
}

.mana-bar-container {
    flex-direction: row;
    align-items: center;
}

.mana-label {
    color: rgb(255, 255, 255);
    font-size: 12px;
    width: 25px;
    margin-right: 5px;
}

.mana-bar {
    width: 120px;
    height: 8px;
    background-color: rgb(50, 50, 50);
    border-radius: 4px;
    margin-right: 5px;
}

.mana-fill {
    height: 100%;
    background-color: rgb(33, 150, 243);
    border-radius: 4px;
    transition: width 0.3s ease-in-out;
}

.mana-text {
    color: rgb(255, 255, 255);
    font-size: 10px;
    min-width: 40px;
}

.status-effects {
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 5px;
}

.status-effect {
    width: 16px;
    height: 16px;
    margin: 1px;
    border-radius: 2px;
    background-color: rgb(128, 0, 128);
}

/* Enemy Info */
.enemy-info {
    border-top: 1px solid rgba(74, 144, 226, 0.5);
    padding-top: 10px;
}

.enemy-title {
    color: rgb(255, 255, 255);
    font-size: 14px;
    margin-bottom: 8px;
    -unity-text-align: middle-center;
}

.enemy-details {
    flex-direction: column;
}

/* Battle Log */
.battle-log-container {
    position: absolute;
    bottom: 20px;
    left: 20px;
    width: 300px;
    height: 150px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    padding: 10px;
}

.log-title {
    color: rgb(255, 255, 255);
    font-size: 14px;
    margin-bottom: 5px;
}

.battle-log {
    flex-grow: 1;
}

.log-text {
    color: rgb(204, 204, 204);
    font-size: 12px;
    white-space: normal;
}

/* Targeting UI */
.targeting-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
}

.targeting-instruction {
    position: absolute;
    top: 50px;
    left: 50%;
    translate: -50% 0;
    color: rgb(255, 255, 255);
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 5px;
}

.targeting-cursor {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 2px solid rgb(106, 176, 255);
    border-radius: 20px;
    background-color: rgba(106, 176, 255, 0.3);
}

.cursor-inner {
    width: 100%;
    height: 100%;
    border: 1px solid rgb(255, 255, 255);
    border-radius: 18px;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Damage Numbers */
.damage-numbers-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Battle Results */
.battle-results {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
}

.results-panel {
    background-color: rgba(0, 0, 0, 0.9);
    border: 2px solid rgb(74, 144, 226);
    border-radius: 10px;
    padding: 30px;
    min-width: 500px;
    max-width: 700px;
}

.results-title {
    color: rgb(255, 255, 255);
    font-size: 32px;
    margin-bottom: 20px;
    -unity-text-align: middle-center;
}

.experience-section,
.rewards-section,
.stats-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: rgba(26, 58, 110, 0.3);
    border-radius: 5px;
}

.exp-title,
.rewards-title,
.stats-title {
    color: rgb(255, 255, 255);
    font-size: 18px;
    margin-bottom: 10px;
}

.stat-label {
    color: rgb(204, 204, 204);
    font-size: 14px;
    margin-bottom: 5px;
}

.continue-button {
    padding: 12px 30px;
    background-color: rgb(26, 58, 110);
    color: rgb(255, 255, 255);
    border-width: 2px;
    border-color: rgb(74, 144, 226);
    border-radius: 5px;
    font-size: 18px;
    align-self: center;
    margin-top: 20px;
}

.continue-button:hover {
    background-color: rgb(42, 90, 158);
    border-color: rgb(106, 176, 255);
}

/* Utility Classes */
.hidden {
    display: none;
}

.fade-in {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

.fade-out {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.glow {
    border-color: rgb(106, 176, 255);
    background-color: rgba(106, 176, 255, 0.2);
}
