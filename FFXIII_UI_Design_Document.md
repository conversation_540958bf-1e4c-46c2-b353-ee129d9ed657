# Final Fantasy XIII Combat UI Design Document

## Overview
This document outlines the design and implementation plan for integrating Final Fantasy XIII-inspired combat UI elements into our existing tactical combat system using Unity's UI Toolkit.

## FF XIII Combat UI Analysis

### Key Visual Elements Identified

#### 1. ATB (Active Time Battle) Gauge System
- **Visual Design**: Segmented horizontal bars that fill progressively
- **Functionality**: Each segment represents an action slot that charges over time
- **Integration**: Adapt to our turn-based system by showing "readiness" for actions
- **Location**: Bottom-left area of screen, per character

#### 2. Command Menu System
- **Visual Design**: Vertical list with clean, modern styling
- **Categories**: Attack, Magic, Items, Paradigm Shift
- **Navigation**: Smooth transitions between menu levels
- **Styling**: Dark backgrounds with blue accent colors, clean typography

#### 3. Character Status Displays
- **HP/MP Bars**: Horizontal bars with gradient fills (green to red for HP, blue for MP)
- **Character Portraits**: Circular or rounded rectangular frames
- **Status Effects**: Small icons with duration indicators
- **Location**: Right side panel for party members

#### 4. Target Selection Interface
- **Visual Feedback**: Glowing outlines, cursor indicators
- **Enemy Information**: HP bars, weakness indicators, status effects
- **Smooth Transitions**: Animated cursor movement between targets

#### 5. Action Queue/Timeline
- **Visual Design**: Horizontal timeline showing upcoming actions
- **Elements**: Character portraits, action icons, timing indicators
- **Location**: Top or bottom of screen

#### 6. Damage Numbers
- **Style**: Large, bold numbers with motion blur effects
- **Colors**: White for normal damage, yellow for critical, green for healing
- **Animation**: Pop-up with arc trajectory and fade-out

## Current System Integration Points

### Existing Architecture Strengths
1. **UI Toolkit Foundation**: Already using UXML/USS for modern UI development
2. **Event-Driven System**: BattleManager provides comprehensive event system
3. **Character System**: Robust BaseCharacter with stats and abilities
4. **Targeting System**: Existing TargetingSystem for action selection
5. **Turn Management**: BattleManager handles turn order and flow

### Integration Strategy

#### 1. Extend Existing UI Framework
- Build upon current BattleUI.cs and UI Toolkit structure
- Maintain compatibility with existing IBattleUI interface
- Enhance current UXML/USS files with FF XIII styling

#### 2. Event System Integration
- Leverage existing BattleManager events:
  - OnTurnStarted → Update ATB gauges and active character
  - OnCharacterDamaged/Healed → Trigger damage numbers
  - OnStatusEffectApplied → Update status displays
  - OnBattleStarted/Ended → Initialize/cleanup UI

#### 3. Data Binding Patterns
- Create reactive UI components that automatically update
- Use existing Character stats system for real-time updates
- Implement observer pattern for UI state management

## Technical Implementation Plan

### Phase 1: Core Architecture
1. **FF13UIController**: Main controller managing all FF XIII UI components
2. **UIDataBinding**: System for reactive UI updates
3. **FF13StyleManager**: Centralized styling and theme management
4. **AnimationController**: Handle UI animations and transitions

### Phase 2: Component Implementation
1. **ATBGaugeComponent**: Individual character ATB gauge
2. **CommandMenuComponent**: Main action selection menu
3. **CharacterStatusComponent**: Enhanced character status display
4. **TargetSelectionComponent**: Target highlighting and selection
5. **ActionQueueComponent**: Timeline display for actions
6. **DamageNumberComponent**: Animated damage/healing numbers

### Phase 3: Integration and Polish
1. Connect components to existing battle system
2. Implement smooth animations and transitions
3. Add audio feedback integration points
4. Performance optimization and testing

## Visual Design Specifications

### Color Palette (FF XIII Inspired)
- **Primary**: #1a3a6e (Dark Blue)
- **Secondary**: #4a90e2 (Light Blue)
- **Accent**: #6ab0ff (Bright Blue)
- **Success**: #4CAF50 (Green)
- **Warning**: #FF9800 (Orange)
- **Danger**: #F44336 (Red)
- **Background**: rgba(0, 0, 0, 0.8) (Semi-transparent Black)

### Typography
- **Primary Font**: Clean, modern sans-serif (similar to existing)
- **Sizes**: 
  - Headers: 24px
  - Body: 18px
  - Small: 14px
  - Damage Numbers: 32px+

### Layout Structure
```
┌─────────────────────────────────────────────────────────┐
│ Action Queue Timeline                                   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  Battle Area                                           │
│                                                         │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ ATB Gauges    │                        │ Party Status   │
│ (Bottom-Left) │     Command Menu       │ (Right Panel)  │
│               │     (Center-Bottom)    │                │
└─────────────────────────────────────────────────────────┘
```

## File Structure Plan

### New UI Components
```
Assets/
├── UI/
│   ├── FFXIII/
│   │   ├── UXML/
│   │   │   ├── FF13BattleUI.uxml
│   │   │   ├── ATBGauge.uxml
│   │   │   ├── CommandMenu.uxml
│   │   │   ├── CharacterStatus.uxml
│   │   │   ├── ActionQueue.uxml
│   │   │   └── DamageNumbers.uxml
│   │   ├── USS/
│   │   │   ├── FF13Theme.uss
│   │   │   ├── ATBGauge.uss
│   │   │   ├── CommandMenu.uss
│   │   │   └── Animations.uss
│   │   └── Scripts/
│   │       ├── FF13UIController.cs
│   │       ├── Components/
│   │       │   ├── ATBGaugeComponent.cs
│   │       │   ├── CommandMenuComponent.cs
│   │       │   ├── CharacterStatusComponent.cs
│   │       │   ├── TargetSelectionComponent.cs
│   │       │   ├── ActionQueueComponent.cs
│   │       │   └── DamageNumberComponent.cs
│   │       └── Core/
│   │           ├── UIDataBinding.cs
│   │           ├── FF13StyleManager.cs
│   │           └── UIAnimationController.cs
```

## FF XIII Specific Mechanics to Implement

### ATB Gauge Details
- **Segments**: 3-6 segments per character (based on level/paradigm)
- **Charging**: Real-time filling, but adapted for turn-based (instant fill on turn start)
- **Action Cost**: Different actions consume different numbers of segments
- **Visual States**: Empty (dark), Charging (animated fill), Full (bright glow)

### Paradigm System Adaptation
- **Paradigm Roles**: Commando (physical), Ravager (magic), Medic (healing), etc.
- **Quick Switch**: Fast paradigm changes during battle
- **Visual Indicators**: Role icons, color coding, stat modifications
- **Menu Integration**: Paradigm selection in command menu

### Command Menu Behavior
- **Auto-Battle Option**: Quick action selection for faster gameplay
- **Action Categories**: Attack, Magic, Items, Paradigm
- **Contextual Actions**: Menu options change based on current paradigm
- **Visual Feedback**: Smooth animations, clear selection indicators

## Implementation Priority

### High Priority (Core Functionality)
1. ATB Gauge visualization and integration
2. Enhanced command menu with FF XIII styling
3. Character status displays with real-time updates
4. Basic damage number system

### Medium Priority (Enhanced Features)
1. Paradigm system integration
2. Action queue timeline
3. Advanced animations and transitions
4. Target selection enhancements

### Low Priority (Polish)
1. Advanced visual effects
2. Audio integration
3. Performance optimizations
4. Accessibility features

## Technical Considerations

### Performance
- Use UI Toolkit's efficient rendering
- Pool damage number objects
- Optimize animation updates
- Minimize layout recalculations

### Scalability
- Support different screen resolutions
- Flexible layout system
- Configurable UI elements
- Modular component architecture

### Maintainability
- Clear separation of concerns
- Comprehensive documentation
- Unit tests for UI logic
- Version control for UI assets

## Next Steps
1. Begin implementation with Core UI Architecture Setup
2. Create foundational components and data binding system
3. Implement individual UI components in phases
4. Integrate with existing battle system
5. Add polish, animations, and final styling

This design maintains compatibility with the existing system while providing a comprehensive FF XIII-inspired visual overhaul that enhances the tactical combat experience.
