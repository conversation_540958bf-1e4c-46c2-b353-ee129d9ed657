using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.UI.FFXIII.Core
{
    /// <summary>
    /// Reactive data binding system for FF XIII UI components
    /// Provides automatic UI updates when game data changes
    /// </summary>
    public class UIDataBinding
    {
        private Dictionary<string, IBindableProperty> properties;
        private Dictionary<VisualElement, List<IBinding>> elementBindings;
        
        public UIDataBinding()
        {
            properties = new Dictionary<string, IBindableProperty>();
            elementBindings = new Dictionary<VisualElement, List<IBinding>>();
        }
        
        #region Property Management
        
        /// <summary>
        /// Create a bindable property
        /// </summary>
        public BindableProperty<T> CreateProperty<T>(string key, T initialValue = default(T))
        {
            var property = new BindableProperty<T>(initialValue);
            properties[key] = property;
            return property;
        }
        
        /// <summary>
        /// Get a bindable property
        /// </summary>
        public BindableProperty<T> GetProperty<T>(string key)
        {
            if (properties.TryGetValue(key, out var property) && property is BindableProperty<T> typedProperty)
            {
                return typedProperty;
            }
            return null;
        }
        
        /// <summary>
        /// Set property value
        /// </summary>
        public void SetProperty<T>(string key, T value)
        {
            var property = GetProperty<T>(key);
            if (property != null)
            {
                property.Value = value;
            }
        }
        
        /// <summary>
        /// Get property value
        /// </summary>
        public T GetPropertyValue<T>(string key)
        {
            var property = GetProperty<T>(key);
            return property != null ? property.Value : default(T);
        }
        
        #endregion
        
        #region Element Binding
        
        /// <summary>
        /// Bind a visual element property to a data property
        /// </summary>
        public void BindElement<T>(VisualElement element, string propertyName, string dataKey, 
            Func<T, object> converter = null)
        {
            var dataProperty = GetProperty<T>(dataKey);
            if (dataProperty == null)
            {
                Debug.LogWarning($"UIDataBinding: Property '{dataKey}' not found for binding");
                return;
            }
            
            var binding = new ElementBinding<T>(element, propertyName, dataProperty, converter);
            
            if (!elementBindings.ContainsKey(element))
            {
                elementBindings[element] = new List<IBinding>();
            }
            
            elementBindings[element].Add(binding);
            
            // Apply initial value
            binding.UpdateElement();
        }
        
        /// <summary>
        /// Bind element text to a data property
        /// </summary>
        public void BindText<T>(VisualElement element, string dataKey, Func<T, string> formatter = null)
        {
            BindElement(element, "text", dataKey, value => 
                formatter != null ? formatter(value) : value?.ToString() ?? "");
        }
        
        /// <summary>
        /// Bind element style property to a data property
        /// </summary>
        public void BindStyle<T>(VisualElement element, string styleName, string dataKey, 
            Func<T, StyleValue> converter)
        {
            var dataProperty = GetProperty<T>(dataKey);
            if (dataProperty == null)
            {
                Debug.LogWarning($"UIDataBinding: Property '{dataKey}' not found for style binding");
                return;
            }
            
            var binding = new StyleBinding<T>(element, styleName, dataProperty, converter);
            
            if (!elementBindings.ContainsKey(element))
            {
                elementBindings[element] = new List<IBinding>();
            }
            
            elementBindings[element].Add(binding);
            
            // Apply initial value
            binding.UpdateElement();
        }
        
        /// <summary>
        /// Unbind all bindings for an element
        /// </summary>
        public void UnbindElement(VisualElement element)
        {
            if (elementBindings.TryGetValue(element, out var bindings))
            {
                foreach (var binding in bindings)
                {
                    binding.Dispose();
                }
                elementBindings.Remove(element);
            }
        }
        
        #endregion
        
        #region Character Data Binding
        
        /// <summary>
        /// Create bindings for a character's data
        /// </summary>
        public void CreateCharacterBindings(BaseCharacter character, string prefix = "")
        {
            var keyPrefix = string.IsNullOrEmpty(prefix) ? character.Name : $"{prefix}_{character.Name}";
            
            // Health bindings
            var healthProperty = CreateProperty($"{keyPrefix}_health", character.CurrentHealth);
            var maxHealthProperty = CreateProperty($"{keyPrefix}_maxHealth", character.MaxHealth);
            var healthPercentProperty = CreateProperty($"{keyPrefix}_healthPercent", 
                character.MaxHealth > 0 ? (float)character.CurrentHealth / character.MaxHealth : 0f);
            
            // Mana bindings
            var manaProperty = CreateProperty($"{keyPrefix}_mana", character.CurrentMana);
            var maxManaProperty = CreateProperty($"{keyPrefix}_maxMana", character.MaxMana);
            var manaPercentProperty = CreateProperty($"{keyPrefix}_manaPercent", 
                character.MaxMana > 0 ? (float)character.CurrentMana / character.MaxMana : 0f);
            
            // Status bindings
            var isAliveProperty = CreateProperty($"{keyPrefix}_isAlive", character.IsAlive);
            var isActiveProperty = CreateProperty($"{keyPrefix}_isActive", false);
            
            // Subscribe to character events for automatic updates
            character.OnHealthChanged += (current, max) =>
            {
                healthProperty.Value = current;
                maxHealthProperty.Value = max;
                healthPercentProperty.Value = max > 0 ? (float)current / max : 0f;
                isAliveProperty.Value = current > 0;
            };
            
            character.OnManaChanged += (current, max) =>
            {
                manaProperty.Value = current;
                maxManaProperty.Value = max;
                manaPercentProperty.Value = max > 0 ? (float)current / max : 0f;
            };
        }
        
        /// <summary>
        /// Update character active state
        /// </summary>
        public void SetCharacterActive(BaseCharacter character, bool isActive, string prefix = "")
        {
            var keyPrefix = string.IsNullOrEmpty(prefix) ? character.Name : $"{prefix}_{character.Name}";
            SetProperty($"{keyPrefix}_isActive", isActive);
        }
        
        #endregion
        
        #region Cleanup
        
        /// <summary>
        /// Cleanup all bindings and properties
        /// </summary>
        public void Cleanup()
        {
            // Dispose all bindings
            foreach (var bindings in elementBindings.Values)
            {
                foreach (var binding in bindings)
                {
                    binding.Dispose();
                }
            }
            
            elementBindings.Clear();
            
            // Dispose all properties
            foreach (var property in properties.Values)
            {
                property.Dispose();
            }
            
            properties.Clear();
        }
        
        #endregion
    }
    
    #region Bindable Property System
    
    /// <summary>
    /// Interface for bindable properties
    /// </summary>
    public interface IBindableProperty : IDisposable
    {
        event Action OnValueChanged;
    }
    
    /// <summary>
    /// Generic bindable property that notifies when value changes
    /// </summary>
    public class BindableProperty<T> : IBindableProperty
    {
        private T _value;
        
        public T Value
        {
            get => _value;
            set
            {
                if (!EqualityComparer<T>.Default.Equals(_value, value))
                {
                    _value = value;
                    OnValueChanged?.Invoke();
                }
            }
        }
        
        public event Action OnValueChanged;
        
        public BindableProperty(T initialValue = default(T))
        {
            _value = initialValue;
        }
        
        public void Dispose()
        {
            OnValueChanged = null;
        }
    }
    
    #endregion
    
    #region Binding System
    
    /// <summary>
    /// Interface for UI bindings
    /// </summary>
    public interface IBinding : IDisposable
    {
        void UpdateElement();
    }
    
    /// <summary>
    /// Binding between a visual element property and a data property
    /// </summary>
    public class ElementBinding<T> : IBinding
    {
        private readonly VisualElement element;
        private readonly string propertyName;
        private readonly BindableProperty<T> dataProperty;
        private readonly Func<T, object> converter;
        
        public ElementBinding(VisualElement element, string propertyName, 
            BindableProperty<T> dataProperty, Func<T, object> converter = null)
        {
            this.element = element;
            this.propertyName = propertyName;
            this.dataProperty = dataProperty;
            this.converter = converter;
            
            dataProperty.OnValueChanged += UpdateElement;
        }
        
        public void UpdateElement()
        {
            if (element == null) return;
            
            var value = converter != null ? converter(dataProperty.Value) : dataProperty.Value;
            
            // Use reflection to set the property
            var elementType = element.GetType();
            var property = elementType.GetProperty(propertyName);
            
            if (property != null && property.CanWrite)
            {
                try
                {
                    property.SetValue(element, value);
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"Failed to set property '{propertyName}' on element: {ex.Message}");
                }
            }
        }
        
        public void Dispose()
        {
            if (dataProperty != null)
            {
                dataProperty.OnValueChanged -= UpdateElement;
            }
        }
    }
    
    /// <summary>
    /// Binding between a visual element style and a data property
    /// </summary>
    public class StyleBinding<T> : IBinding
    {
        private readonly VisualElement element;
        private readonly string styleName;
        private readonly BindableProperty<T> dataProperty;
        private readonly Func<T, StyleValue> converter;
        
        public StyleBinding(VisualElement element, string styleName, 
            BindableProperty<T> dataProperty, Func<T, StyleValue> converter)
        {
            this.element = element;
            this.styleName = styleName;
            this.dataProperty = dataProperty;
            this.converter = converter;
            
            dataProperty.OnValueChanged += UpdateElement;
        }
        
        public void UpdateElement()
        {
            if (element == null || converter == null) return;
            
            var styleValue = converter(dataProperty.Value);
            
            // Apply style based on style name
            switch (styleName.ToLower())
            {
                case "width":
                    element.style.width = styleValue;
                    break;
                case "height":
                    element.style.height = styleValue;
                    break;
                case "opacity":
                    element.style.opacity = styleValue;
                    break;
                case "display":
                    element.style.display = styleValue;
                    break;
                case "backgroundcolor":
                    element.style.backgroundColor = styleValue;
                    break;
                case "color":
                    element.style.color = styleValue;
                    break;
                default:
                    Debug.LogWarning($"Unsupported style property: {styleName}");
                    break;
            }
        }
        
        public void Dispose()
        {
            if (dataProperty != null)
            {
                dataProperty.OnValueChanged -= UpdateElement;
            }
        }
    }
    
    #endregion
}
