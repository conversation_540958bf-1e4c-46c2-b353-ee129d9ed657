using UnityEngine;
using UnityEngine.UIElements;

namespace TacticalCombatSystem.UI.FFXIII.Core
{
    /// <summary>
    /// Manages FF XIII-inspired styling and theming for UI components
    /// Provides centralized color schemes, fonts, and visual effects
    /// </summary>
    public class FF13StyleManager
    {
        #region Color Palette
        
        // Primary Colors (FF XIII Inspired)
        public static readonly Color PrimaryDark = new Color(0.1f, 0.23f, 0.43f, 1f);      // #1a3a6e
        public static readonly Color PrimaryLight = new Color(0.29f, 0.56f, 0.89f, 1f);    // #4a90e2
        public static readonly Color AccentBlue = new Color(0.42f, 0.69f, 1f, 1f);         // #6ab0ff
        
        // Status Colors
        public static readonly Color HealthGreen = new Color(0.3f, 0.69f, 0.31f, 1f);      // #4CAF50
        public static readonly Color ManaBlue = new Color(0.13f, 0.59f, 0.95f, 1f);        // #2196F3
        public static readonly Color WarningOrange = new Color(1f, 0.6f, 0f, 1f);          // #FF9800
        public static readonly Color DangerRed = new Color(0.96f, 0.26f, 0.21f, 1f);       // #F44336
        
        // Background Colors
        public static readonly Color BackgroundDark = new Color(0f, 0f, 0f, 0.8f);         // Semi-transparent black
        public static readonly Color BackgroundMedium = new Color(0f, 0f, 0f, 0.6f);       // Medium transparency
        public static readonly Color BackgroundLight = new Color(0f, 0f, 0f, 0.4f);        // Light transparency
        
        // Text Colors
        public static readonly Color TextPrimary = Color.white;
        public static readonly Color TextSecondary = new Color(0.8f, 0.8f, 0.8f, 1f);
        public static readonly Color TextDisabled = new Color(0.5f, 0.5f, 0.5f, 1f);
        
        // Special Effect Colors
        public static readonly Color GlowBlue = new Color(0.42f, 0.69f, 1f, 0.5f);
        public static readonly Color CriticalYellow = new Color(1f, 0.84f, 0f, 1f);        // #FFD700
        public static readonly Color HealingGreen = new Color(0.56f, 1f, 0.56f, 1f);       // #90FF90
        
        #endregion
        
        #region Style Constants
        
        // Border Radius
        public const float BorderRadiusSmall = 3f;
        public const float BorderRadiusMedium = 5f;
        public const float BorderRadiusLarge = 8f;
        
        // Padding and Margins
        public const float PaddingSmall = 5f;
        public const float PaddingMedium = 10f;
        public const float PaddingLarge = 20f;
        
        // Font Sizes
        public const float FontSizeSmall = 14f;
        public const float FontSizeMedium = 18f;
        public const float FontSizeLarge = 24f;
        public const float FontSizeXLarge = 32f;
        
        // Animation Durations
        public const float AnimationFast = 0.15f;
        public const float AnimationMedium = 0.3f;
        public const float AnimationSlow = 0.5f;
        
        #endregion
        
        #region Theme Application
        
        /// <summary>
        /// Apply FF XIII theme to a root visual element
        /// </summary>
        public void ApplyTheme(VisualElement root)
        {
            if (root == null) return;
            
            // Apply base theme styles
            ApplyBaseStyles(root);
            
            // Apply component-specific styles
            ApplyATBGaugeStyles(root);
            ApplyCommandMenuStyles(root);
            ApplyCharacterStatusStyles(root);
            ApplyActionQueueStyles(root);
        }
        
        /// <summary>
        /// Apply base styles to the root element
        /// </summary>
        private void ApplyBaseStyles(VisualElement root)
        {
            root.style.flexGrow = 1;
            root.style.flexDirection = FlexDirection.Column;
            root.style.justifyContent = Justify.SpaceBetween;
            root.style.padding = new StyleLength(PaddingLarge);
            root.style.color = TextPrimary;
        }
        
        /// <summary>
        /// Apply ATB gauge specific styles
        /// </summary>
        private void ApplyATBGaugeStyles(VisualElement root)
        {
            var atbContainer = root.Q<VisualElement>("atb-container");
            if (atbContainer != null)
            {
                atbContainer.style.flexDirection = FlexDirection.Column;
                atbContainer.style.position = Position.Absolute;
                atbContainer.style.bottom = PaddingLarge;
                atbContainer.style.left = PaddingLarge;
                atbContainer.style.backgroundColor = BackgroundMedium;
                atbContainer.style.borderTopLeftRadius = BorderRadiusMedium;
                atbContainer.style.borderTopRightRadius = BorderRadiusMedium;
                atbContainer.style.borderBottomLeftRadius = BorderRadiusMedium;
                atbContainer.style.borderBottomRightRadius = BorderRadiusMedium;
                atbContainer.style.padding = new StyleLength(PaddingMedium);
            }
            
            // Style ATB gauge elements
            var atbGauges = root.Query<VisualElement>(className: "atb-gauge").ToList();
            foreach (var gauge in atbGauges)
            {
                StyleATBGauge(gauge);
            }
        }
        
        /// <summary>
        /// Apply command menu specific styles
        /// </summary>
        private void ApplyCommandMenuStyles(VisualElement root)
        {
            var menuContainer = root.Q<VisualElement>("command-menu-container");
            if (menuContainer != null)
            {
                menuContainer.style.position = Position.Absolute;
                menuContainer.style.bottom = PaddingLarge;
                menuContainer.style.left = new StyleLength(new Length(50, LengthUnit.Percent));
                menuContainer.style.translate = new StyleTranslate(new Translate(new Length(-50, LengthUnit.Percent), 0));
                menuContainer.style.backgroundColor = BackgroundDark;
                menuContainer.style.borderTopLeftRadius = BorderRadiusMedium;
                menuContainer.style.borderTopRightRadius = BorderRadiusMedium;
                menuContainer.style.borderBottomLeftRadius = BorderRadiusMedium;
                menuContainer.style.borderBottomRightRadius = BorderRadiusMedium;
                menuContainer.style.borderLeftColor = AccentBlue;
                menuContainer.style.borderRightColor = AccentBlue;
                menuContainer.style.borderTopColor = AccentBlue;
                menuContainer.style.borderBottomColor = AccentBlue;
                menuContainer.style.borderLeftWidth = 2f;
                menuContainer.style.borderRightWidth = 2f;
                menuContainer.style.borderTopWidth = 2f;
                menuContainer.style.borderBottomWidth = 2f;
                menuContainer.style.padding = new StyleLength(PaddingMedium);
                menuContainer.style.minWidth = 200f;
            }
            
            // Style menu buttons
            var menuButtons = root.Query<Button>(className: "menu-button").ToList();
            foreach (var button in menuButtons)
            {
                StyleMenuButton(button);
            }
        }
        
        /// <summary>
        /// Apply character status specific styles
        /// </summary>
        private void ApplyCharacterStatusStyles(VisualElement root)
        {
            var statusContainer = root.Q<VisualElement>("character-status-container");
            if (statusContainer != null)
            {
                statusContainer.style.position = Position.Absolute;
                statusContainer.style.top = PaddingLarge;
                statusContainer.style.right = PaddingLarge;
                statusContainer.style.flexDirection = FlexDirection.Column;
                statusContainer.style.backgroundColor = BackgroundMedium;
                statusContainer.style.borderTopLeftRadius = BorderRadiusMedium;
                statusContainer.style.borderTopRightRadius = BorderRadiusMedium;
                statusContainer.style.borderBottomLeftRadius = BorderRadiusMedium;
                statusContainer.style.borderBottomRightRadius = BorderRadiusMedium;
                statusContainer.style.padding = new StyleLength(PaddingMedium);
                statusContainer.style.minWidth = 300f;
            }
            
            // Style character cards
            var characterCards = root.Query<VisualElement>(className: "character-card").ToList();
            foreach (var card in characterCards)
            {
                StyleCharacterCard(card);
            }
        }
        
        /// <summary>
        /// Apply action queue specific styles
        /// </summary>
        private void ApplyActionQueueStyles(VisualElement root)
        {
            var queueContainer = root.Q<VisualElement>("action-queue-container");
            if (queueContainer != null)
            {
                queueContainer.style.position = Position.Absolute;
                queueContainer.style.top = PaddingLarge;
                queueContainer.style.left = new StyleLength(new Length(50, LengthUnit.Percent));
                queueContainer.style.translate = new StyleTranslate(new Translate(new Length(-50, LengthUnit.Percent), 0));
                queueContainer.style.flexDirection = FlexDirection.Row;
                queueContainer.style.backgroundColor = BackgroundLight;
                queueContainer.style.borderTopLeftRadius = BorderRadiusMedium;
                queueContainer.style.borderTopRightRadius = BorderRadiusMedium;
                queueContainer.style.borderBottomLeftRadius = BorderRadiusMedium;
                queueContainer.style.borderBottomRightRadius = BorderRadiusMedium;
                queueContainer.style.padding = new StyleLength(PaddingSmall);
            }
        }
        
        #endregion
        
        #region Component Styling
        
        /// <summary>
        /// Style an ATB gauge element
        /// </summary>
        public void StyleATBGauge(VisualElement gauge)
        {
            if (gauge == null) return;
            
            gauge.style.flexDirection = FlexDirection.Row;
            gauge.style.alignItems = Align.Center;
            gauge.style.marginBottom = PaddingSmall;
            
            // Style character name
            var nameLabel = gauge.Q<Label>("character-name");
            if (nameLabel != null)
            {
                nameLabel.style.color = TextPrimary;
                nameLabel.style.fontSize = FontSizeMedium;
                nameLabel.style.width = 120f;
                nameLabel.style.marginRight = PaddingMedium;
            }
            
            // Style ATB segments container
            var segmentsContainer = gauge.Q<VisualElement>("atb-segments");
            if (segmentsContainer != null)
            {
                segmentsContainer.style.flexDirection = FlexDirection.Row;
                segmentsContainer.style.height = 20f;
                segmentsContainer.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
                segmentsContainer.style.borderTopLeftRadius = BorderRadiusSmall;
                segmentsContainer.style.borderTopRightRadius = BorderRadiusSmall;
                segmentsContainer.style.borderBottomLeftRadius = BorderRadiusSmall;
                segmentsContainer.style.borderBottomRightRadius = BorderRadiusSmall;
            }
        }
        
        /// <summary>
        /// Style a menu button
        /// </summary>
        public void StyleMenuButton(Button button)
        {
            if (button == null) return;
            
            button.style.marginTop = PaddingSmall;
            button.style.marginBottom = PaddingSmall;
            button.style.paddingTop = 8f;
            button.style.paddingBottom = 8f;
            button.style.paddingLeft = 15f;
            button.style.paddingRight = 15f;
            button.style.backgroundColor = PrimaryDark;
            button.style.color = TextPrimary;
            button.style.borderLeftColor = PrimaryLight;
            button.style.borderRightColor = PrimaryLight;
            button.style.borderTopColor = PrimaryLight;
            button.style.borderBottomColor = PrimaryLight;
            button.style.borderLeftWidth = 1f;
            button.style.borderRightWidth = 1f;
            button.style.borderTopWidth = 1f;
            button.style.borderBottomWidth = 1f;
            button.style.borderTopLeftRadius = BorderRadiusSmall;
            button.style.borderTopRightRadius = BorderRadiusSmall;
            button.style.borderBottomLeftRadius = BorderRadiusSmall;
            button.style.borderBottomRightRadius = BorderRadiusSmall;
            button.style.unityTextAlign = TextAnchor.MiddleLeft;
            
            // Add hover effects
            button.RegisterCallback<MouseEnterEvent>(evt =>
            {
                button.style.backgroundColor = new Color(0.16f, 0.35f, 0.62f, 1f); // Lighter blue
                button.style.borderLeftColor = AccentBlue;
                button.style.borderRightColor = AccentBlue;
                button.style.borderTopColor = AccentBlue;
                button.style.borderBottomColor = AccentBlue;
            });
            
            button.RegisterCallback<MouseLeaveEvent>(evt =>
            {
                button.style.backgroundColor = PrimaryDark;
                button.style.borderLeftColor = PrimaryLight;
                button.style.borderRightColor = PrimaryLight;
                button.style.borderTopColor = PrimaryLight;
                button.style.borderBottomColor = PrimaryLight;
            });
        }
        
        /// <summary>
        /// Style a character card
        /// </summary>
        public void StyleCharacterCard(VisualElement card)
        {
            if (card == null) return;
            
            card.style.flexDirection = FlexDirection.Row;
            card.style.justifyContent = Justify.SpaceBetween;
            card.style.alignItems = Align.Center;
            card.style.padding = new StyleLength(PaddingSmall);
            card.style.marginBottom = PaddingSmall;
            card.style.backgroundColor = new Color(0.1f, 0.1f, 0.1f, 0.5f);
            card.style.borderTopLeftRadius = BorderRadiusSmall;
            card.style.borderTopRightRadius = BorderRadiusSmall;
            card.style.borderBottomLeftRadius = BorderRadiusSmall;
            card.style.borderBottomRightRadius = BorderRadiusSmall;
            
            // Style character name
            var nameLabel = card.Q<Label>("character-name");
            if (nameLabel != null)
            {
                nameLabel.style.color = TextPrimary;
                nameLabel.style.fontSize = FontSizeMedium;
                nameLabel.style.width = 120f;
            }
            
            // Style health bar
            var healthBar = card.Q<VisualElement>("health-bar");
            if (healthBar != null)
            {
                StyleHealthBar(healthBar);
            }
            
            // Style mana bar
            var manaBar = card.Q<VisualElement>("mana-bar");
            if (manaBar != null)
            {
                StyleManaBar(manaBar);
            }
        }
        
        /// <summary>
        /// Style a health bar
        /// </summary>
        public void StyleHealthBar(VisualElement healthBar)
        {
            if (healthBar == null) return;
            
            healthBar.style.width = 100f;
            healthBar.style.height = 15f;
            healthBar.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            healthBar.style.borderTopLeftRadius = 7f;
            healthBar.style.borderTopRightRadius = 7f;
            healthBar.style.borderBottomLeftRadius = 7f;
            healthBar.style.borderBottomRightRadius = 7f;
            
            var healthFill = healthBar.Q<VisualElement>("health-fill");
            if (healthFill != null)
            {
                healthFill.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
                healthFill.style.backgroundColor = HealthGreen;
                healthFill.style.borderTopLeftRadius = 7f;
                healthFill.style.borderTopRightRadius = 7f;
                healthFill.style.borderBottomLeftRadius = 7f;
                healthFill.style.borderBottomRightRadius = 7f;
            }
        }
        
        /// <summary>
        /// Style a mana bar
        /// </summary>
        public void StyleManaBar(VisualElement manaBar)
        {
            if (manaBar == null) return;
            
            manaBar.style.width = 100f;
            manaBar.style.height = 10f;
            manaBar.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            manaBar.style.borderTopLeftRadius = 5f;
            manaBar.style.borderTopRightRadius = 5f;
            manaBar.style.borderBottomLeftRadius = 5f;
            manaBar.style.borderBottomRightRadius = 5f;
            
            var manaFill = manaBar.Q<VisualElement>("mana-fill");
            if (manaFill != null)
            {
                manaFill.style.height = new StyleLength(new Length(100, LengthUnit.Percent));
                manaFill.style.backgroundColor = ManaBlue;
                manaFill.style.borderTopLeftRadius = 5f;
                manaFill.style.borderTopRightRadius = 5f;
                manaFill.style.borderBottomLeftRadius = 5f;
                manaFill.style.borderBottomRightRadius = 5f;
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Get health color based on percentage
        /// </summary>
        public static Color GetHealthColor(float healthPercent)
        {
            if (healthPercent > 0.6f) return HealthGreen;
            if (healthPercent > 0.3f) return WarningOrange;
            return DangerRed;
        }
        
        /// <summary>
        /// Get mana color based on percentage
        /// </summary>
        public static Color GetManaColor(float manaPercent)
        {
            return Color.Lerp(new Color(0.1f, 0.3f, 0.6f, 1f), ManaBlue, manaPercent);
        }
        
        /// <summary>
        /// Create a glow effect color
        /// </summary>
        public static Color CreateGlowColor(Color baseColor, float intensity = 0.5f)
        {
            return new Color(baseColor.r, baseColor.g, baseColor.b, intensity);
        }
        
        #endregion
    }
}
