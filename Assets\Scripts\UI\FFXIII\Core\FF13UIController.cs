using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Core;

namespace TacticalCombatSystem.UI.FFXIII.Core
{
    /// <summary>
    /// Main controller for Final Fantasy XIII-inspired combat UI system
    /// Manages all FF XIII UI components and coordinates with the existing battle system
    /// </summary>
    public class FF13UIController : MonoBehaviour, IBattleUI
    {
        [Header("UI Documents")]
        [SerializeField] private UIDocument mainUIDocument;
        [SerializeField] private VisualTreeAsset ff13BattleUIAsset;
        
        [Header("Component References")]
        [SerializeField] private BattleManager battleManager;
        [SerializeField] private TargetingSystem targetingSystem;
        
        [Header("Settings")]
        [SerializeField] private bool enableATBGauges = true;
        [SerializeField] private bool enableParadigmSystem = true;
        [SerializeField] private bool enableActionQueue = true;
        [SerializeField] private float uiAnimationSpeed = 1.0f;
        
        // Core UI Components
        private ATBGaugeManager atbGaugeManager;
        private CommandMenuController commandMenuController;
        private CharacterStatusManager characterStatusManager;
        private TargetSelectionController targetSelectionController;
        private ActionQueueController actionQueueController;
        private DamageNumberController damageNumberController;
        
        // UI Data Binding
        private UIDataBinding dataBinding;
        private FF13StyleManager styleManager;
        private UIAnimationController animationController;
        
        // UI State
        private BaseCharacter currentActiveCharacter;
        private bool isUIInitialized = false;
        private Dictionary<BaseCharacter, CharacterUIData> characterUIData;
        
        // Events
        public event Action<BaseCharacter> OnCharacterSelected;
        public event Action<ICombatAction> OnActionSelected;
        public event Action OnMenuClosed;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeComponents();
            characterUIData = new Dictionary<BaseCharacter, CharacterUIData>();
        }
        
        private void Start()
        {
            if (battleManager != null)
            {
                SubscribeToBattleEvents();
            }
            
            InitializeUI();
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromBattleEvents();
            CleanupUI();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeComponents()
        {
            // Initialize core systems
            dataBinding = new UIDataBinding();
            styleManager = new FF13StyleManager();
            animationController = new UIAnimationController();
            
            // Initialize component managers
            atbGaugeManager = new ATBGaugeManager(dataBinding, styleManager);
            commandMenuController = new CommandMenuController(dataBinding, animationController);
            characterStatusManager = new CharacterStatusManager(dataBinding, styleManager);
            targetSelectionController = new TargetSelectionController(targetingSystem, animationController);
            actionQueueController = new ActionQueueController(dataBinding, animationController);
            damageNumberController = new DamageNumberController();
        }
        
        private void InitializeUI()
        {
            if (mainUIDocument == null || ff13BattleUIAsset == null)
            {
                Debug.LogError("FF13UIController: Missing UI Document or Battle UI Asset references!");
                return;
            }
            
            // Set the visual tree asset
            mainUIDocument.visualTreeAsset = ff13BattleUIAsset;
            
            // Get root visual element
            var root = mainUIDocument.rootVisualElement;
            
            // Initialize all UI components
            InitializeATBGauges(root);
            InitializeCommandMenu(root);
            InitializeCharacterStatus(root);
            InitializeTargetSelection(root);
            InitializeActionQueue(root);
            
            // Apply FF XIII styling
            styleManager.ApplyTheme(root);
            
            isUIInitialized = true;
        }
        
        private void InitializeATBGauges(VisualElement root)
        {
            if (!enableATBGauges) return;
            
            var atbContainer = root.Q<VisualElement>("atb-container");
            if (atbContainer != null)
            {
                atbGaugeManager.Initialize(atbContainer);
            }
        }
        
        private void InitializeCommandMenu(VisualElement root)
        {
            var menuContainer = root.Q<VisualElement>("command-menu-container");
            if (menuContainer != null)
            {
                commandMenuController.Initialize(menuContainer);
                commandMenuController.OnActionSelected += HandleActionSelected;
                commandMenuController.OnMenuClosed += HandleMenuClosed;
            }
        }
        
        private void InitializeCharacterStatus(VisualElement root)
        {
            var statusContainer = root.Q<VisualElement>("character-status-container");
            if (statusContainer != null)
            {
                characterStatusManager.Initialize(statusContainer);
            }
        }
        
        private void InitializeTargetSelection(VisualElement root)
        {
            targetSelectionController.Initialize();
            targetSelectionController.OnTargetSelected += HandleTargetSelected;
        }
        
        private void InitializeActionQueue(VisualElement root)
        {
            if (!enableActionQueue) return;
            
            var queueContainer = root.Q<VisualElement>("action-queue-container");
            if (queueContainer != null)
            {
                actionQueueController.Initialize(queueContainer);
            }
        }
        
        #endregion
        
        #region Battle Event Handling
        
        private void SubscribeToBattleEvents()
        {
            if (battleManager == null) return;
            
            battleManager.OnBattleStarted += HandleBattleStarted;
            battleManager.OnTurnStarted += HandleTurnStarted;
            battleManager.OnTurnEnded += HandleTurnEnded;
            battleManager.OnCharacterDamaged += HandleCharacterDamaged;
            battleManager.OnCharacterHealed += HandleCharacterHealed;
            battleManager.OnStatusEffectApplied += HandleStatusEffectApplied;
            battleManager.OnBattleEnded += HandleBattleEnded;
        }
        
        private void UnsubscribeFromBattleEvents()
        {
            if (battleManager == null) return;
            
            battleManager.OnBattleStarted -= HandleBattleStarted;
            battleManager.OnTurnStarted -= HandleTurnStarted;
            battleManager.OnTurnEnded -= HandleTurnEnded;
            battleManager.OnCharacterDamaged -= HandleCharacterDamaged;
            battleManager.OnCharacterHealed -= HandleCharacterHealed;
            battleManager.OnStatusEffectApplied -= HandleStatusEffectApplied;
            battleManager.OnBattleEnded -= HandleBattleEnded;
        }
        
        private void HandleBattleStarted()
        {
            if (!isUIInitialized) return;
            
            // Initialize character data
            var playerTeam = battleManager.GetPlayerTeam();
            var enemyTeam = battleManager.GetEnemyTeam();
            
            SetupCharacterData(playerTeam, enemyTeam);
            
            // Initialize UI components with character data
            atbGaugeManager.SetupCharacters(playerTeam);
            characterStatusManager.SetupCharacters(playerTeam, enemyTeam);
            actionQueueController.SetupBattle(playerTeam, enemyTeam);
            
            // Show UI
            ShowBattleUI();
        }
        
        private void HandleTurnStarted(ICombatParticipant participant)
        {
            if (participant is BaseCharacter character)
            {
                currentActiveCharacter = character;
                
                // Update ATB gauges
                atbGaugeManager.SetActiveCharacter(character);
                
                // Update character status displays
                characterStatusManager.SetActiveCharacter(character);
                
                // Show/hide command menu based on player control
                if (character.IsPlayerControlled)
                {
                    ShowCommandMenu(character);
                }
                else
                {
                    HideCommandMenu();
                }
                
                // Update action queue
                actionQueueController.OnTurnStarted(character);
            }
        }
        
        private void HandleTurnEnded(ICombatParticipant participant)
        {
            if (participant is BaseCharacter character)
            {
                // Update ATB gauges
                atbGaugeManager.OnTurnEnded(character);
                
                // Update action queue
                actionQueueController.OnTurnEnded(character);
                
                // Hide command menu
                HideCommandMenu();
            }
        }
        
        private void HandleCharacterDamaged(ICombatParticipant participant, int damage)
        {
            if (participant is BaseCharacter character)
            {
                // Update character status
                characterStatusManager.UpdateCharacterHealth(character);
                
                // Show damage numbers
                damageNumberController.ShowDamage(character.transform.position, damage, false);
                
                // Update ATB gauge if needed
                atbGaugeManager.UpdateCharacterStatus(character);
            }
        }
        
        private void HandleCharacterHealed(ICombatParticipant participant, int healing)
        {
            if (participant is BaseCharacter character)
            {
                // Update character status
                characterStatusManager.UpdateCharacterHealth(character);
                
                // Show healing numbers
                damageNumberController.ShowHealing(character.transform.position, healing, false);
                
                // Update ATB gauge if needed
                atbGaugeManager.UpdateCharacterStatus(character);
            }
        }
        
        private void HandleStatusEffectApplied(ICombatParticipant participant, object statusEffect)
        {
            if (participant is BaseCharacter character)
            {
                // Update character status display
                characterStatusManager.UpdateStatusEffects(character);
                
                // Show status effect notification
                if (statusEffect != null)
                {
                    damageNumberController.ShowStatusEffect(character.transform.position, statusEffect.ToString());
                }
            }
        }
        
        private void HandleBattleEnded(bool playerWon)
        {
            // Hide battle UI
            HideBattleUI();
            
            // Show battle results (to be implemented)
            // ShowBattleResults(playerWon);
        }
        
        #endregion
        
        #region UI Control Methods
        
        private void SetupCharacterData(List<ICombatParticipant> playerTeam, List<ICombatParticipant> enemyTeam)
        {
            characterUIData.Clear();
            
            foreach (var participant in playerTeam)
            {
                if (participant is BaseCharacter character)
                {
                    characterUIData[character] = new CharacterUIData(character);
                }
            }
            
            foreach (var participant in enemyTeam)
            {
                if (participant is BaseCharacter character)
                {
                    characterUIData[character] = new CharacterUIData(character);
                }
            }
        }
        
        private void ShowBattleUI()
        {
            if (mainUIDocument != null)
            {
                mainUIDocument.rootVisualElement.style.display = DisplayStyle.Flex;
                animationController.FadeIn(mainUIDocument.rootVisualElement, 0.5f);
            }
        }
        
        private void HideBattleUI()
        {
            if (mainUIDocument != null)
            {
                animationController.FadeOut(mainUIDocument.rootVisualElement, 0.5f, () =>
                {
                    mainUIDocument.rootVisualElement.style.display = DisplayStyle.None;
                });
            }
        }
        
        private void ShowCommandMenu(BaseCharacter character)
        {
            commandMenuController.ShowMenu(character);
        }
        
        private void HideCommandMenu()
        {
            commandMenuController.HideMenu();
        }
        
        private void CleanupUI()
        {
            // Cleanup all components
            atbGaugeManager?.Cleanup();
            commandMenuController?.Cleanup();
            characterStatusManager?.Cleanup();
            targetSelectionController?.Cleanup();
            actionQueueController?.Cleanup();
            damageNumberController?.Cleanup();
            
            // Cleanup data binding
            dataBinding?.Cleanup();
        }
        
        #endregion
        
        #region Event Handlers
        
        private void HandleActionSelected(ICombatAction action)
        {
            OnActionSelected?.Invoke(action);
            
            // Start targeting if needed
            if (currentActiveCharacter != null && targetingSystem != null)
            {
                targetingSystem.StartTargeting(currentActiveCharacter, action);
                targetSelectionController.StartTargeting(action);
            }
        }
        
        private void HandleTargetSelected(ICombatParticipant target)
        {
            // Execute action through battle manager
            if (currentActiveCharacter != null && targetingSystem != null)
            {
                var action = targetingSystem.GetCurrentAction();
                if (action != null)
                {
                    battleManager.UseAbility(currentActiveCharacter, target, action);
                    battleManager.OnPlayerActionSelected();
                }
            }
            
            // Hide command menu
            HideCommandMenu();
        }
        
        private void HandleMenuClosed()
        {
            OnMenuClosed?.Invoke();
        }
        
        #endregion
        
        #region IBattleUI Implementation
        
        public void SetBattleMenuActive(bool active)
        {
            if (active && currentActiveCharacter != null && currentActiveCharacter.IsPlayerControlled)
            {
                ShowCommandMenu(currentActiveCharacter);
            }
            else
            {
                HideCommandMenu();
            }
        }
        
        public void UpdateCharacterUI(BaseCharacter character)
        {
            characterStatusManager?.UpdateCharacterStatus(character);
            atbGaugeManager?.UpdateCharacterStatus(character);
        }
        
        public void ShowTargetingUI(bool show)
        {
            if (show)
            {
                targetSelectionController?.ShowTargeting();
            }
            else
            {
                targetSelectionController?.HideTargeting();
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Data container for character UI information
    /// </summary>
    public class CharacterUIData
    {
        public BaseCharacter Character { get; private set; }
        public float ATBProgress { get; set; }
        public int ATBSegments { get; set; }
        public bool IsActive { get; set; }
        
        public CharacterUIData(BaseCharacter character)
        {
            Character = character;
            ATBProgress = 0f;
            ATBSegments = 3; // Default segments
            IsActive = false;
        }
    }
}
