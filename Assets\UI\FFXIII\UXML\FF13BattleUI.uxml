<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/FFXIII/USS/FF13Theme.uss?fileID=7433441132597879392&amp;guid=ff13theme&amp;type=3#FF13Theme" />
    
    <!-- Root Container -->
    <ui:VisualElement name="ff13-battle-ui-root" class="ff13-battle-ui-root">
        
        <!-- Action Queue Timeline (Top) -->
        <ui:VisualElement name="action-queue-container" class="action-queue-container">
            <ui:Label name="action-queue-title" text="Action Queue" class="action-queue-title" />
            <ui:VisualElement name="action-queue-timeline" class="action-queue-timeline">
                <!-- Action queue items will be dynamically added here -->
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Main Battle Area (Center) -->
        <ui:VisualElement name="battle-area" class="battle-area">
            <!-- This area is for the 3D battle scene -->
            <!-- Target selection indicators and damage numbers appear here -->
        </ui:VisualElement>
        
        <!-- Bottom UI Container -->
        <ui:VisualElement name="bottom-ui-container" class="bottom-ui-container">
            
            <!-- ATB Gauges (Bottom Left) -->
            <ui:VisualElement name="atb-container" class="atb-container">
                <ui:Label name="atb-title" text="ATB" class="atb-title" />
                <ui:VisualElement name="atb-gauges" class="atb-gauges">
                    <!-- ATB gauges will be dynamically added here -->
                </ui:VisualElement>
            </ui:VisualElement>
            
            <!-- Command Menu (Bottom Center) -->
            <ui:VisualElement name="command-menu-container" class="command-menu-container">
                <ui:VisualElement name="main-menu" class="main-menu">
                    <ui:Button name="attack-button" text="Attack" class="menu-button" />
                    <ui:Button name="magic-button" text="Magic" class="menu-button" />
                    <ui:Button name="items-button" text="Items" class="menu-button" />
                    <ui:Button name="paradigm-button" text="Paradigm" class="menu-button" />
                    <ui:Button name="defend-button" text="Defend" class="menu-button" />
                </ui:VisualElement>
                
                <!-- Sub-menus (initially hidden) -->
                <ui:VisualElement name="attack-submenu" class="submenu" style="display: none;">
                    <ui:Label name="submenu-title" text="Attack" class="submenu-title" />
                    <ui:ScrollView name="attack-list" class="action-list">
                        <!-- Attack actions will be dynamically added here -->
                    </ui:ScrollView>
                    <ui:Button name="back-button" text="Back" class="back-button" />
                </ui:VisualElement>
                
                <ui:VisualElement name="magic-submenu" class="submenu" style="display: none;">
                    <ui:Label name="submenu-title" text="Magic" class="submenu-title" />
                    <ui:ScrollView name="magic-list" class="action-list">
                        <!-- Magic actions will be dynamically added here -->
                    </ui:ScrollView>
                    <ui:Button name="back-button" text="Back" class="back-button" />
                </ui:VisualElement>
                
                <ui:VisualElement name="items-submenu" class="submenu" style="display: none;">
                    <ui:Label name="submenu-title" text="Items" class="submenu-title" />
                    <ui:ScrollView name="items-list" class="action-list">
                        <!-- Item actions will be dynamically added here -->
                    </ui:ScrollView>
                    <ui:Button name="back-button" text="Back" class="back-button" />
                </ui:VisualElement>
                
                <ui:VisualElement name="paradigm-submenu" class="submenu" style="display: none;">
                    <ui:Label name="submenu-title" text="Paradigm Shift" class="submenu-title" />
                    <ui:VisualElement name="paradigm-list" class="paradigm-list">
                        <!-- Paradigm options will be dynamically added here -->
                    </ui:VisualElement>
                    <ui:Button name="back-button" text="Back" class="back-button" />
                </ui:VisualElement>
            </ui:VisualElement>
            
        </ui:VisualElement>
        
        <!-- Character Status Panel (Right) -->
        <ui:VisualElement name="character-status-container" class="character-status-container">
            <ui:Label name="party-title" text="Party Status" class="party-title" />
            <ui:VisualElement name="party-members" class="party-members">
                <!-- Character status cards will be dynamically added here -->
            </ui:VisualElement>
            
            <ui:VisualElement name="enemy-info" class="enemy-info">
                <ui:Label name="enemy-title" text="Target Info" class="enemy-title" />
                <ui:VisualElement name="enemy-details" class="enemy-details">
                    <!-- Enemy information will be dynamically added here -->
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Battle Log (Optional, can be toggled) -->
        <ui:VisualElement name="battle-log-container" class="battle-log-container" style="display: none;">
            <ui:Label name="log-title" text="Battle Log" class="log-title" />
            <ui:ScrollView name="battle-log" class="battle-log">
                <ui:Label name="log-text" class="log-text" />
            </ui:ScrollView>
        </ui:VisualElement>
        
        <!-- Targeting UI Overlay -->
        <ui:VisualElement name="targeting-overlay" class="targeting-overlay" style="display: none;">
            <ui:Label name="targeting-instruction" text="Select Target" class="targeting-instruction" />
            <ui:VisualElement name="targeting-cursor" class="targeting-cursor">
                <ui:VisualElement name="cursor-inner" class="cursor-inner" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Damage Numbers Container -->
        <ui:VisualElement name="damage-numbers-container" class="damage-numbers-container">
            <!-- Damage numbers will be dynamically added here -->
        </ui:VisualElement>
        
        <!-- Battle Results Screen (Initially hidden) -->
        <ui:VisualElement name="battle-results" class="battle-results" style="display: none;">
            <ui:VisualElement name="results-panel" class="results-panel">
                <ui:Label name="results-title" text="Victory!" class="results-title" />
                
                <ui:VisualElement name="experience-section" class="experience-section">
                    <ui:Label name="exp-title" text="Experience Gained" class="exp-title" />
                    <ui:VisualElement name="exp-details" class="exp-details">
                        <!-- Experience details will be dynamically added here -->
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:VisualElement name="rewards-section" class="rewards-section">
                    <ui:Label name="rewards-title" text="Items Obtained" class="rewards-title" />
                    <ui:VisualElement name="rewards-list" class="rewards-list">
                        <!-- Reward items will be dynamically added here -->
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:VisualElement name="stats-section" class="stats-section">
                    <ui:Label name="stats-title" text="Battle Statistics" class="stats-title" />
                    <ui:VisualElement name="battle-stats" class="battle-stats">
                        <ui:Label name="time-stat" text="Time: 00:00" class="stat-label" />
                        <ui:Label name="damage-stat" text="Damage Dealt: 0" class="stat-label" />
                        <ui:Label name="healing-stat" text="Healing Done: 0" class="stat-label" />
                        <ui:Label name="paradigm-stat" text="Paradigm Shifts: 0" class="stat-label" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <ui:Button name="continue-button" text="Continue" class="continue-button" />
            </ui:VisualElement>
        </ui:VisualElement>
        
    </ui:VisualElement>
</ui:UXML>
